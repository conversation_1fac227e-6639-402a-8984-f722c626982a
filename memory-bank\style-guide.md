# style guide

> this document defines coding style guidelines specific to this project.

## code formatting

-   indentation: 4 spaces (no tabs)
-   line length: 88 characters max (black default)
-   use black for auto-formatting
-   flake8 for linting

## naming conventions

-   modules: lowercase_with_underscores
-   classes: CapitalizedWords (camelcase)
-   functions/methods: lowercase_with_underscores
-   variables: lowercase_with_underscores
-   constants: ALL_CAPS_WITH_UNDERSCORES

## documentation

-   docstrings for all modules, classes, functions, and methods
-   google-style docstring format
-   clear comments for complex logic
-   readme.md for project overview
-   logging in lowercase

## imports

-   import organization:
    1. standard library imports
    2. third-party libraries
    3. local application imports
-   no wildcard imports (from x import \*)
-   prefer explicit imports

## error handling

-   use explicit exception types
-   meaningful error messages
-   proper logging of errors
-   graceful degradation when possible

## testing

-   test file names: test\_\*.py
-   test function names: test\_\*
-   meaningful test descriptions
