# technical context

> this document outlines the technical stack, dependencies, and development environment.

## tech stack

-   python 3.x
-   audio processing libraries (likely ffmpeg-based)
-   possibly speech recognition for transcription
-   translation apis or libraries

## development environment

-   windows 11 / ubuntu 24.00 lts in wsl
-   powershell 7 / fish shell
-   package manager: uv (instead of pip)
-   uses pre-commit hooks for code quality

## dependencies

-   based on pyproject.toml and imports:
    -   audio processing libraries
    -   transcription tools
    -   translation services
    -   utility libraries

## build & deployment

-   package built with standard python packaging tools
-   commands available through cli interface
-   configuration through json files

## testing approach

-   unit tests for individual components
-   integration tests for workflows
-   validation of audio processing results

## best practices

-   adhere to pep8 style guidelines
-   docstrings for all public functions and classes
-   error handling with appropriate logging
-   configuration management through json
