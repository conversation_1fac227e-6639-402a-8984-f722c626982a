[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "smart-chunker"
description = "A tool for chunking audio and aligning transcripts"
readme = "README.md"
requires-python = ">=3.10"
license = { text = "MIT" }
version = "0.1.0"
dependencies = [
  "annotated-types==0.7.0",
  "anyio==4.9.0",
  "black>=25.1.0",
  "cachetools==5.5.2",
  "certifi==2025.4.26",
  "charset-normalizer==3.4.2",
  "colorama==0.4.6",
  "dotenv==0.9.9",
  "elevenlabs==1.58.0",
  "flake8>=7.2.0",
  "google-auth==2.39.0",
  "google-genai==1.14.0",
  "h11==0.14.0",
  "httpcore==1.0.8",
  "httpx==0.28.1",
  "idna==3.10",
  "isort>=6.0.1",
  "loguru==0.7.3",
  "numpy==2.2.5",
  "pyasn1==0.6.1",
  "pyasn1-modules==0.4.2",
  "pydantic==2.11.4",
  "pydantic-core==2.33.2",
  "pydantic-settings==2.9.1",
  "pydub==0.25.1",
  "python-dotenv==1.1.0",
  "pyyaml==6.0.2",
  "requests==2.32.3",
  "rsa==4.9.1",
  "sniffio==1.3.1",
  "srt==3.5.3",
  "tenacity==9.1.2",
  "typing-extensions==4.13.2",
  "typing-inspection==0.4.0",
  "urllib3==2.4.0",
  "websockets==15.0.1",
  "win32-setctime==1.2.0",
  "torch",
  "torchvision",
  "torchaudio",
  "demucs>=4.0.1",
  "soundfile",
]

[project.optional-dependencies]
dev = ["pytest", "black", "isort", "flake8", "flake8-docstrings", "pre-commit"]

[tool.setuptools.packages.find]
where = ["."]

[project.scripts]
smart-chunker = "smart_chunker.cli.entrypoint:main"

# Tool configuration
[tool.black]
line-length = 100
target-version = ["py37", "py38", "py39", "py310"]
include = '\.pyi?$'

[tool.isort]
profile = "black"
line_length = 100
known_first_party = ["smart_chunker"]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]

[[tool.uv.index]]
name = "pytorch-cu126"
url = "https://download.pytorch.org/whl/cu126"
explicit = true

[tool.uv.sources]
torch = [{ index = "pytorch-cu126" }]
torchvision = [{ index = "pytorch-cu126" }]
torchaudio = [{ index = "pytorch-cu126" }]
