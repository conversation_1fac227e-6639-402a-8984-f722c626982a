# smart chunker

an all-in-one python toolkit for processing long form audio/video into manageable pieces, aligning transcripts, translating content, and generating speech — built to streamline multimedia workflows.

## table of contents

-   [features](#features)
-   [architecture overview](#architecture-overview)
-   [quick start](#quick-start)
-   [command-line usage](#command-line-usage)
-   [programmatic usage](#programmatic-usage)
-   [configuration](#configuration)
-   [development setup](#development-setup)
-   [testing](#testing)
-   [contributing](#contributing)
-   [roadmap](#roadmap)
-   [license](#license)

## features

| area                | capabilities                                                                                                                                                                                                    |
| ------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| audio processing    | • split audio by silence or fixed length<br>• **integrated audio separation** with demucs (vocals/drums/bass/other)<br>• extract timestamps for precise segmenting<br>• silence detection with multiple methods |
| transcript handling | • align srt/vtt transcripts to audio chunks<br>• concatenate or re-segment transcripts                                                                                                                          |
| translation         | • batch translate transcripts via external apis with rate-limiting and smart retries                                                                                                                            |
| text-to-speech      | • convert subtitle files to natural speech with dynamic speed control and parallel workers                                                                                                                      |
| utilities           | • unified logging with loguru<br>• robust retry helpers<br>• modular architecture with clean imports                                                                                                            |

## architecture overview

```
smart_chunker/
├── core/              # fundamental utilities
│   ├── config_manager.py    # configuration loading and validation
│   ├── file_ops.py         # filesystem operations
│   └── __init__.py
├── processing/        # main processing logic
│   ├── segments.py         # audio segmentation and timestamp extraction
│   ├── transcript.py       # transcript processing and srt export
│   ├── translation.py      # translation request analysis and batch processing
│   ├── audio_separation.py # audio separation pipeline integration
│   └── __init__.py
├── pipeline/          # orchestration
│   ├── main.py            # smartchunkerpipeline class
│   └── __init__.py
├── cli/               # command-line interface
│   ├── entrypoint.py      # main cli entry point
│   └── __init__.py
├── audio/             # audio processing utilities
│   ├── separation.py       # audio source separation with demucs
│   ├── silence_detector.py # silence detection and speech segmentation
│   ├── splitter.py         # audio splitting by silence
│   └── timestamp_extractor.py # timestamp extraction
├── transcript/        # transcript alignment and manipulation
├── translation/       # translation engines and processors
├── utils/             # logging, retry helpers
└── config/            # default and runtime configs

smart_srt_tts/         # dedicated srt → tts package
```

components are designed to be importable individually or orchestrated together via the pipeline. the modular architecture allows for clean separation of concerns and easy testing.

## quick start

```bash
# clone repository
git clone https://github.com/ubranch/smart-chunker.git
cd smart-chunker

# create virtual environment (powershell example)
python -m venv .venv
.\.venv\scripts\activate

# install runtime deps with uv (faster pip replacement)
uv pip install .
```

verify installation:

```powershell
smart-chunker --help
```

## command-line usage

the main entry point is defined in `pyproject.toml` as `smart-chunker = smart_chunker.cli.entrypoint:main`.

### run full processing pipeline

```powershell
# recommended - clean module execution
python -m smart_chunker

# alternative - cli submodule
python -m smart_chunker.cli

# with custom config
python -m smart_chunker --config path\to\your_config.json

# if installed via pip/uv
smart-chunker --config path\to\your_config.json
```

the pipeline processes audio/video files through the complete workflow: **audio separation** → segmentation → transcript processing → translation → output generation.

### audio separation

smart chunker includes integrated audio source separation using demucs, allowing you to separate audio into vocal and instrumental stems before processing. this can significantly improve transcription quality by isolating vocals.

```powershell
# enable audio separation in your config file
{
    "input_processing": {
        "audio_separation": {
            "enabled": true,
            "model": "htdemucs",
            "stems": "vocals",
            "device": "auto"
        }
    }
}

# run pipeline with separation enabled
python -m smart_chunker --config config_with_separation.json
```

when enabled, the pipeline automatically:

1. separates the input audio into stems (vocals, drums, bass, other)
2. uses the separated vocals for transcription (if available)
3. saves all separated stems to the output directory
4. falls back to original audio if separation fails

## programmatic usage

### using the pipeline

```python
from smart_chunker.pipeline.main import smartchunkerpipeline

# initialize pipeline with config
pipeline = smartchunkerpipeline("smart_chunker/config/run_config.json")

# run end-to-end processing
pipeline.run()
```

### using individual modules

```python
# core utilities
from smart_chunker.core import load_config, ensure_output_directory

# processing modules
from smart_chunker.processing import segments, transcript, translation, audio_separation

# audio processing
from smart_chunker.audio.separation import AudioSeparator, AudioSeparationConfig

# load configuration
config = load_config("path/to/config.json")

# optional: perform audio separation
separation_result = audio_separation.process_audio_separation(
    "input.mp4", config.raw, "run_id_123", "output_dir"
)

# segment audio and extract timestamps
seg_meta, timestamps, temp_dir = segments.segment_audio_and_extract_timestamps(
    "input.mp4", config.raw, "run_id_123"
)

# process transcript
full_transcript = transcript.concatenate_transcript(timestamps)
chunks, _ = transcript.segment_transcript(full_transcript, "input", "output", "run_id")

# translate content
style_req = translation.analyze_translation_request(full_transcript, "uz", "en")
translated = translation.translate_transcript_chunks(chunks, full_transcript, "uz", "en", style_req)
```

## configuration

runtime behaviour is driven by json config files located in `smart_chunker/config/` (see `run_config.json` for an example). key sections include:

-   `input_processing` – original file path, segment duration, silence detection settings, **audio separation configuration**
-   `translation` – source/target languages for translation
-   `performance` – max parallel workers, rate limiting (rpm)
-   `logging` – log level configuration
-   `output` – base directory for results
-   `models` – ai model configurations for different processing stages

example configuration structure:

```json
{
    "input_processing": {
        "original_file": "data/input/video.mp4",
        "min_segment_duration_seconds": 60,
        "use_temporary_directory_for_segments": true,
        "silence_detection": {
            "method": "pydub",
            "min_silence_len_ms": 500,
            "abs_threshold_db": -30.0,
            "speech_segment_padding_ms": 200,
            "max_silence_to_bridge_seconds": 2.0
        },
        "audio_separation": {
            "enabled": false,
            "model": "htdemucs",
            "stems": "vocals",
            "output_format": "wav",
            "device": "auto"
        }
    },
    "translation": {
        "source_language": "uz",
        "target_language": "en"
    },
    "performance": {
        "max_parallel_workers": 150,
        "rate_limit_rpm": 150
    },
    "logging": {
        "level": "DEBUG"
    },
    "output": {
        "base_directory": "data/output"
    }
}
```

environment variables can override any json field (e.g., `$env:elevenlabs_api_key`).

## development setup

```powershell
# install dev extras
uv pip install -e ".[dev]"

# install pre-commit hooks
pre-commit install

# run style checks (if available)
# python scripts\dev\check_style.py
```

format code automatically:

```powershell
# use black and isort for formatting
black .
isort .
```

### module structure

the codebase follows a clean modular architecture:

-   **`smart_chunker.core`** - fundamental utilities (config, file operations)
-   **`smart_chunker.processing`** - main processing logic (segments, transcript, translation, audio separation)
-   **`smart_chunker.pipeline`** - orchestration and workflow management
-   **`smart_chunker.cli`** - command-line interface
-   **`smart_chunker.audio`** - audio processing utilities (separation, silence detection, splitting)
-   **`smart_chunker.transcript`** - transcript manipulation
-   **`smart_chunker.translation`** - translation engines
-   **`smart_chunker.utils`** - logging and retry helpers

### import guidelines

use the new modular imports:

```python
# ✅ preferred - new structure
from smart_chunker.core import load_config
from smart_chunker.processing import segments, audio_separation
from smart_chunker.audio.separation import AudioSeparator
from smart_chunker.pipeline.main import smartchunkerpipeline

# ❌ avoid - old flat imports (removed)
# from smart_chunker.config_manager import load_config
```

## testing

```powershell
pytest
```

test the restructured imports:

```powershell
# test core functionality
python -c "from smart_chunker.core import load_config; print('✓ core works')"

# test processing modules
python -c "from smart_chunker.processing import segments; print('✓ processing works')"

# test pipeline
python -c "from smart_chunker.pipeline.main import smartchunkerpipeline; print('✓ pipeline works')"

# test cli
python -m smart_chunker --help
```

## contributing

1. fork repository and create feature branch (`git checkout -b feat/your-feature`).
2. follow style guidelines in `memory-bank/style-guide.md`.
3. use the new modular structure for any new code.
4. commit using conventional commits (e.g., `feat(processing): add new segmentation method`).
5. open pull request targeting `main` (default branch currently `unknown`).

issues and feature requests are welcome via github issues.

## roadmap

-   ✅ **modular architecture** - clean separation of concerns
-   ✅ **integrated audio separation** - demucs-powered stem separation
-   packaging to pypi
-   interactive web ui built with litestar
-   gpu-accelerated batch processing pipeline
-   plugin framework for third-party tts/translation providers
-   comprehensive test suite
-   api documentation with sphinx

## license

mit © 2025 ubranch
