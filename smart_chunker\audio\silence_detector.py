"""silence_detector.py – robust silence‑/speech‑gap detection utility

features
--------
* works on **any audio/video** file that ffmpeg understands (mp3, wav, mp4 …).
* two detection engines:
    • **pydub** (default) – fast, pure‑python; good for ≤30‑min clips.
    • **ffmpeg** – streams, uses almost no ram; scales to hours‑long media.
* threshold can be **relative** to average loudness (e.g. 16 db below mean),
  or an **absolute** dbfs value.
* pretty timestamp formatting ``hh:mm:ss.mmm`` and json/csv export.
* sensible cli with progress logging.
"""

from __future__ import annotations

import json
import re
import subprocess
from dataclasses import dataclass
from enum import Enum, auto
from pathlib import Path
from typing import Any, Dict, Final, List, Optional, Union

from loguru import logger

# third‑party
from pydub import AudioSegment
from pydub.silence import detect_silence

# ---------------------------------------------------------------------------
# constants
# ---------------------------------------------------------------------------
_MILLIS: Final[int] = 1000
_TIMESTAMP_RE = re.compile(r"^(\d+):(\d+):(\d+)(?:[.,](\d{1,3}))?$")
_OUTPUT_DIR = "data/output"


# ---------------------------------------------------------------------------
# data structures
# ---------------------------------------------------------------------------
@dataclass(slots=True)
class Range:
    """represents a time range with start and end in seconds"""

    start: float  # seconds
    end: float  # seconds

    # ─────── representation helpers ───────────────────────────────────────
    def as_dict(self) -> dict[str, float]:
        return {"start": self.start, "end": self.end}

    def __str__(self) -> str:
        return f"{fmt(self.start)} – {fmt(self.end)}"


class DetectionMethod(Enum):
    """detection method options"""

    PYDUB = auto()
    FFMPEG = auto()

    @classmethod
    def from_string(cls, value: str) -> "DetectionMethod":
        """convert string value to enum"""
        mapping = {"pydub": cls.PYDUB, "ffmpeg": cls.FFMPEG}
        if value.lower() not in mapping:
            raise ValueError(f"Invalid detection method: {value}. Use 'pydub' or 'ffmpeg'")
        return mapping[value.lower()]


# ---------------------------------------------------------------------------
# utility functions
# ---------------------------------------------------------------------------
def fmt(seconds: float) -> str:
    """hh:mm:ss.mmm (always zero‑padded to 3‑digit ms)."""
    h, rem = divmod(seconds, 3600)
    m, s = divmod(rem, 60)
    ms = int(round((s - int(s)) * _MILLIS))
    return f"{int(h):02d}:{int(m):02d}:{int(s):02d}.{ms:03d}"


def parse_timestamp(ts: str) -> float:
    """inverse of :func:`fmt` – strict but forgiving on the ms separator."""
    m = _TIMESTAMP_RE.fullmatch(ts.strip())
    if not m:
        raise ValueError(f"Invalid timestamp '{ts}'. Use HH:MM:SS.mmm")
    h, m_, s, ms = m.groups()
    secs = int(h) * 3600 + int(m_) * 60 + int(s)
    if ms:
        secs += int(ms.ljust(3, "0")) / _MILLIS
    return secs


class SilenceDetector:
    """professional silence detector for audio/video files

    provides multiple detection methods, configurable thresholds and output formats
    """

    def __init__(
        self,
        file_path: Union[str, Path],
        method: Union[str, DetectionMethod] = DetectionMethod.PYDUB,
        min_silence_len: int = 1000,
        rel_thresh: Optional[float] = 16.0,
        abs_thresh: Optional[float] = None,
        log_level: str = "INFO",
        downsample_rate: Optional[int] = None,
    ):
        """initialize the silence detector

        args:
            file_path: path to audio/video file
            method: detection method - 'pydub' or 'ffmpeg'
            min_silence_len: minimum silence length in milliseconds
            rel_thresh: db below average to count as silence
            abs_thresh: absolute dbfs threshold (negative value)
            log_level: logging level (INFO, DEBUG, etc.)
            downsample_rate: optional sample rate to downsample audio to (e.g. 16000)
                             for faster processing with pydub method
        """
        # validate file exists
        self.file_path = Path(file_path)
        if not self.file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        # convert string method to enum if needed
        self.method = (
            method if isinstance(method, DetectionMethod) else DetectionMethod.from_string(method)
        )

        # validation
        if rel_thresh is not None and abs_thresh is not None:
            raise ValueError("Choose either rel_thresh or abs_thresh, not both")

        # store configuration
        self.min_silence_len = min_silence_len  # milliseconds
        self.rel_thresh = rel_thresh
        self.abs_thresh = abs_thresh
        self.downsample_rate = downsample_rate

        # using global logger from import instead of local setup
        # this ensures consistent logging format and behavior with main app
        self.log = logger

        # cache for audio info and detection results
        self._audio_info: Optional[Dict[str, Any]] = None
        self._spans: Optional[List[Range]] = None

    @property
    def audio_info(self) -> Dict[str, Any]:
        """return audio file information with lazy loading"""
        if self._audio_info is None:
            self._audio_info = self._get_audio_info()
        return self._audio_info

    @property
    def duration(self) -> float:
        """get audio duration in seconds"""
        return self.audio_info["duration"]

    @property
    def formatted_duration(self) -> str:
        """get audio duration as formatted timestamp"""
        return fmt(self.duration)

    def _get_audio_info(self) -> Dict[str, Any]:
        """return duration (seconds) + ffprobe json block for extra hacking"""
        cmd = [
            "ffprobe",
            "-v",
            "error",
            "-show_entries",
            "format=duration:format_tags:stream_tags",
            "-of",
            "json",
            str(self.file_path),
        ]
        try:
            res = subprocess.run(cmd, text=True, capture_output=True, check=True)
        except subprocess.CalledProcessError as exc:
            self.log.error("ffprobe failed: {}", exc.stderr.strip())
            raise RuntimeError(f"ffprobe failed: {exc.stderr.strip()}")

        info = json.loads(res.stdout)
        duration = float(info["format"]["duration"])
        return {"duration": duration, "ffprobe": info}

    def detect(self) -> List[Range]:
        """detect silence spans using configured method"""
        if self._spans is not None:
            return self._spans

        self.log.info("duration: {} ({:.2f} s)", self.formatted_duration, self.duration)

        if self.method == DetectionMethod.PYDUB:
            self._spans = self._detect_pydub()
        else:
            self._spans = self._detect_ffmpeg()

        return self._spans

    def _detect_pydub(self) -> List[Range]:
        """detect silence ranges using pydub's amplitude‑based method"""
        self.log.info("loading audio with pydub...")
        audio = AudioSegment.from_file(self.file_path)

        # optional downsampling for faster processing
        if self.downsample_rate:
            self.log.info("downsampling to {} hz", self.downsample_rate)
            audio = audio.set_frame_rate(self.downsample_rate)

        # determine threshold
        if self.abs_thresh is not None:
            thresh = self.abs_thresh
            self.log.info("using absolute threshold: {} dbfs", thresh)
        else:
            # calculate relative threshold
            mean_dBFS = audio.dBFS
            thresh = mean_dBFS - self.rel_thresh
            self.log.info(
                "using relative threshold: {} dbfs ({} db below mean of {} dbfs)",
                thresh,
                self.rel_thresh,
                mean_dBFS,
            )

        # detect silence
        self.log.info("detecting silence (min length: {} ms)...", self.min_silence_len)
        result = detect_silence(audio, min_silence_len=self.min_silence_len, silence_thresh=thresh)

        # convert to our range class (ms → seconds)
        spans = [Range(start=start / _MILLIS, end=end / _MILLIS) for start, end in result]

        self.log.info("detected {} silence spans", len(spans))
        return spans

    def _detect_ffmpeg(self) -> List[Range]:
        """detect silence using ffmpeg's silencedetect filter"""
        self.log.info("using ffmpeg silencedetect (streaming method)")

        # determine threshold args for ffmpeg
        if self.abs_thresh is not None:
            thresh_arg = f"n={self.abs_thresh}dB"
            self.log.info("using absolute threshold: {} dbfs", self.abs_thresh)
        else:
            thresh_arg = f"d={self.rel_thresh}dB"
            self.log.info("using relative threshold: {} db below mean", self.rel_thresh)

        cmd = [
            "ffmpeg",
            "-i",
            str(self.file_path),
            "-af",
            f"silencedetect=noise={thresh_arg}:duration={self.min_silence_len/1000}",
            "-f",
            "null",
            "-",
        ]

        self.log.info("running silencedetect...")
        proc = subprocess.run(cmd, text=True, capture_output=True)

        if proc.returncode != 0:
            self.log.error("ffmpeg silencedetect failed: {}", proc.stderr)
            raise RuntimeError(f"ffmpeg silencedetect failed: {proc.stderr}")

        # parse the output - find silence_start and silence_end entries
        # example output:
        # [silencedetect @ 0x7f8a5c014700] silence_start: 3.52
        # [silencedetect @ 0x7f8a5c014700] silence_end: 4.92 | silence_duration: 1.4

        starts = []
        ends = []

        for line in proc.stderr.splitlines():
            if "silence_start" in line:
                ts = float(line.split("silence_start:")[1].strip())
                starts.append(ts)
            elif "silence_end" in line:
                ts = float(line.split("silence_end:")[1].split("|")[0].strip())
                ends.append(ts)

        # sanity check
        if len(starts) != len(ends):
            self.log.warning(
                "mismatch in silence_start ({}) vs silence_end ({}) entries", len(starts), len(ends)
            )
            # handle edge case where file starts or ends with silence
            if len(starts) > len(ends):
                # file ends with silence - add end of file
                ends.append(self.duration)
            else:
                # file starts with silence - add start of file
                starts.insert(0, 0.0)

        # create spans
        spans = [Range(start=start, end=end) for start, end in zip(starts, ends)]

        self.log.info("detected {} silence spans", len(spans))
        return spans

    def get_silence_ranges(self) -> List[Range]:
        """get silence ranges - alias for detect() for clarity"""
        return self.detect()

    def get_speech_segments(self, padding_ms: int = 300) -> List[tuple[float, float]]:
        """get speech segments by inverting silence spans

        Args:
            padding_ms: amount of padding to add to speech segments (ms)

        Returns:
            list of (start, end) tuples in seconds
        """
        # ensure detection has run
        silence_spans = self.detect()
        padding_sec = padding_ms / _MILLIS

        # if no silence detected, whole file is speech
        if not silence_spans:
            self.log.info("no silence detected - treating entire file as speech")
            return [(0, self.duration)]

        # build speech segments by inverting silence spans
        speech_segments = []

        # handle special case: does audio start with silence?
        _file_start = 0.0
        if silence_spans[0].start <= 0.0:
            # starts with silence, first speech starts after first silence
            first_speech_start = silence_spans[0].end
            current_pos = first_speech_start
        else:
            # starts with speech
            first_speech_start = 0.0
            current_pos = 0.0

        # process all silence spans to build speech segments
        for i, span in enumerate(silence_spans):
            # if this silence starts after current position, we have a speech segment
            if span.start > current_pos:
                # create speech segment with padding
                speech_start = max(current_pos - padding_sec, 0)
                speech_end = min(span.start + padding_sec, self.duration)

                # add segment
                speech_segments.append((speech_start, speech_end))

            # move position to end of current silence
            current_pos = span.end

        # check if the last silence ends before the end of the file
        if current_pos < self.duration:
            # add final speech segment with padding
            speech_start = max(current_pos - padding_sec, 0)
            speech_segments.append((speech_start, self.duration))

        # clean up potentially overlapping segments due to padding
        cleaned_segments = []
        for i, segment in enumerate(speech_segments):
            start, end = segment

            # skip very short segments (likely artifacts of padding)
            if end - start < 0.1:  # 100ms
                continue

            # if we already have segments and this one overlaps with the previous
            if cleaned_segments and start <= cleaned_segments[-1][1]:
                # merge with previous segment
                prev_start, prev_end = cleaned_segments[-1]
                cleaned_segments[-1] = (prev_start, max(prev_end, end))
            else:
                # add as a new segment
                cleaned_segments.append((start, end))

        self.log.info("extracted {} speech segments", len(cleaned_segments))
        return cleaned_segments
