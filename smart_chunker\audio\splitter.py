import os
import re
import subprocess
import tempfile
from typing import Any, Dict, List, Optional, Tuple

from loguru import logger


class VideoSplitter:
    """
    a utility class to split an audio (or video) file into segments based on natural silence.
    ... (rest of the docstring) ...
    """

    def __init__(
        self,
        input_file: str,
        min_segment_duration: int = 60,
        silence_noise: str = "-30dB",
        silence_duration: float = 0.5,
        output_dir: Optional[str] = None,
        use_temp_dir: bool = False,
    ) -> None:
        self.input_file = input_file
        self.min_segment_duration = min_segment_duration
        self.silence_noise = silence_noise
        self.silence_duration = silence_duration
        self.use_temp_dir = use_temp_dir

        if self.use_temp_dir:
            # ensure _temp_dir is created before output_dir is assigned
            self._temp_dir = tempfile.TemporaryDirectory()
            self.output_dir = self._temp_dir.name
            logger.info("using temporary directory for segments: {dir}", dir=self.output_dir)
        else:
            self.output_dir = output_dir or os.getcwd()
            # create output directory if it doesn't exist and not using temp dir
            if not os.path.exists(self.output_dir):
                os.makedirs(self.output_dir)
                logger.info("created output directory: {dir}", dir=self.output_dir)
            else:
                logger.info(
                    "using existing output directory for segments: {dir}",
                    dir=self.output_dir,
                )
            # ensure _temp_dir attribute exists even if not used, for __exit__
            self._temp_dir = None

        logger.info(
            "initialized videosplitter for file: {file} with minimum segment duration: {duration}s",
            file=self.input_file,
            duration=self.min_segment_duration,
        )

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        # check if _temp_dir was initialized and is a temporarydirectory instance
        if self.use_temp_dir and hasattr(self, "_temp_dir") and self._temp_dir is not None:
            logger.info("cleaning up temporary directory: {dir}", dir=self._temp_dir.name)
            self._temp_dir.cleanup()
        elif hasattr(self, "_temp_dir") and self._temp_dir is None:
            # log if not using temp dir, just to be clear nothing is cleaned up here
            logger.debug(
                "not using temporary directory, no cleanup needed by videosplitter context manager."
            )

    def _get_video_duration(self) -> float:
        command = [
            "ffprobe",
            "-v",
            "error",
            "-show_entries",
            "format=duration",
            "-of",
            "default=noprint_wrappers=1:nokey=1",
            self.input_file,
        ]
        try:
            result = subprocess.run(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=True,
            )
            duration = float(result.stdout.strip())
            logger.debug("total file duration: {duration} seconds", duration=duration)
            return duration
        except (subprocess.CalledProcessError, FileNotFoundError) as e:
            logger.exception("failed to get video duration using ffprobe: {error}", error=e)
            raise RuntimeError("ffprobe execution failed or not found.") from e
        except ValueError as e:
            logger.exception(
                "failed to parse ffprobe duration output: {output}, error: {error}",
                output=result.stdout,
                error=e,
            )
            raise RuntimeError("could not parse video duration.") from e

    def _detect_silence_points(self) -> List[float]:
        command = [
            "ffmpeg",
            "-i",
            self.input_file,
            "-af",
            f"silencedetect=noise={self.silence_noise}:d={self.silence_duration}",
            "-f",
            "null",
            "-",
        ]
        logger.info("running ffmpeg silencedetect to analyze audio for silence points.")
        try:
            result = subprocess.run(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=False,  # don't check=true, ffmpeg might return non-zero on success here
            )
            # log raw stderr output for debugging
            logger.debug("raw ffmpeg stderr output:\n{}", result.stderr)

            # check stderr for actual errors if return code is non-zero, but silencedetect output is in stderr anyway
            if result.returncode != 0 and "silence_start" not in result.stderr:
                logger.error(
                    "ffmpeg silencedetect failed. stderr: {stderr}",
                    stderr=result.stderr,
                )
                # decide if this is critical. maybe return empty list or raise error.
                # let's raise for now, as silence detection is core.
                raise RuntimeError(f"ffmpeg silencedetect failed: {result.stderr}")

        except FileNotFoundError:
            logger.exception("ffmpeg command not found. ensure ffmpeg is installed and in path.")
            raise RuntimeError("ffmpeg not found.")

        silence_points = []
        pattern = re.compile(r"silence_start:\s*([\d.]+)")
        for line in result.stderr.splitlines():
            match = pattern.search(line)
            if match:
                try:
                    ts = float(match.group(1))
                    # avoid adding duplicate 0.0 start time if detected
                    if ts > 0 or not silence_points:
                        silence_points.append(ts)
                except ValueError:
                    logger.warning(
                        "could not parse timestamp from silencedetect line: {line}",
                        line=line,
                    )

        # log raw unfiltered silence points
        logger.debug("raw unfiltered silence points: {points}", points=silence_points)

        silence_points.sort()
        # ensure 0.0 is the start if not detected (ffmpeg might not report silence at the very beginning)
        if not silence_points or silence_points[0] > 0:
            silence_points.insert(0, 0.0)
            logger.debug("ensured segment start includes 0.0 seconds")

        logger.info(
            "total silence points detected (after processing): {count}",
            count=len(silence_points),
        )
        # log the final list of silence points for analysis
        logger.debug(
            "final silence points (after sorting and processing): {points}",
            points=silence_points,
        )
        return silence_points

    def split_video_by_silence(self) -> List[Dict[str, Any]]:
        """
        splits the video based on silence detection.

        returns:
            List[Dict[str, Any]]: a list of dictionaries, each containing:
                - 'file_path': path to the segment file.
                - 'start_time': start time of the segment in the original file (seconds).
                - 'end_time': end time of the segment in the original file (seconds).
        """
        total_duration = self._get_video_duration()
        silence_points = self._detect_silence_points()
        valid_silence_points = self._filter_silence_points(silence_points, total_duration)
        segments = self._create_segments(valid_silence_points, total_duration)

        self._log_planned_segments(segments)
        return self._process_segments(segments)

    def _filter_silence_points(
        self, silence_points: List[float], total_duration: float
    ) -> List[float]:
        """
        filter silence points to be within reasonable bounds and sort them.

        args:
            silence_points: raw silence points detected
            total_duration: total duration of the video

        returns:
            list of valid silence points, sorted
        """
        # exclude 0 and exact end
        valid_points = [sp for sp in silence_points if sp > 0 and sp < total_duration]
        valid_points.sort()

        # log the valid silence points after filtering
        logger.debug("valid silence points (after filtering): {points}", points=valid_points)
        return valid_points

    def _create_segments(
        self, valid_silence_points: List[float], total_duration: float
    ) -> List[Tuple[float, float]]:
        """
        create segments based on valid silence points.

        args:
            valid_silence_points: list of valid silence points
            total_duration: total duration of the video

        returns:
            list of segments as (start_time, end_time) tuples
        """
        segments = []
        current_start = 0.0
        last_cut_time = 0.0

        while current_start < total_duration:
            target_min_end = current_start + self.min_segment_duration

            # find appropriate cut time
            cut_time = self._find_cut_time(
                valid_silence_points, target_min_end, last_cut_time, total_duration
            )

            # verify cut time validity
            if cut_time <= current_start:
                logger.warning(
                    "calculated cut time {cut:.2f} is not after current start {start:.2f}. breaking loop.",
                    cut=cut_time,
                    start=current_start,
                )
                break

            # prevent segments from exceeding total duration (can happen due to float precision)
            actual_end_time = min(cut_time, total_duration)

            segments.append((current_start, actual_end_time))
            logger.info(
                "segment determined: start={start:.2f} sec, end={end:.2f} sec",
                start=current_start,
                end=actual_end_time,
            )

            last_cut_time = actual_end_time
            current_start = actual_end_time  # start next segment from the end of the current one

            # break if we've reached or passed the end
            if current_start >= total_duration:
                break

        return segments

    def _find_cut_time(
        self,
        valid_silence_points: List[float],
        target_min_end: float,
        last_cut_time: float,
        total_duration: float,
    ) -> float:
        """
        find the appropriate cut time for a segment, searching in both directions.

        args:
            valid_silence_points: list of valid silence points
            target_min_end: minimum end time for the segment
            last_cut_time: time of the last cut
            total_duration: total duration of the video

        returns:
            cut time for the segment
        """
        # default to end of file if no suitable silence found
        cut_time = total_duration

        segment_start = last_cut_time
        min_duration = target_min_end - segment_start

        # define bounds for searching - we want to consider points slightly before min
        # and no more than 2x min after the start
        lower_bound = segment_start + (0.8 * min_duration)  # allow 20% shorter than minimum
        upper_bound = segment_start + (2.0 * min_duration)  # maximum 2x longer than minimum

        # calculate ideal cut point (target is around minimum duration)
        ideal_cut_time = target_min_end

        # find silence points within our flexible range (both before and after min duration)
        candidate_points = [
            sp
            for sp in valid_silence_points
            if sp > last_cut_time  # must be after segment start
            and sp > lower_bound  # don't make segments too short
            and sp < upper_bound  # don't make segments too long
        ]

        # also include all points after min duration up to our upper bound
        standard_points = [
            sp
            for sp in valid_silence_points
            if sp >= target_min_end and sp > last_cut_time and sp < upper_bound
        ]

        # log all points we found
        logger.debug(
            "possible cut points for segment starting at {start:.3f}s (min end at {min_end:.3f}s): {points}",
            start=last_cut_time,
            min_end=target_min_end,
            points=candidate_points,
        )

        if candidate_points:
            # find the silence point closest to ideal cut time (which is our minimum duration)
            closest_point = min(candidate_points, key=lambda sp: abs(sp - ideal_cut_time))

            # if we're significantly below minimum duration and there are points after min, prefer those
            if closest_point < target_min_end and standard_points:
                earliest_after_min = min(standard_points)

                # calculate how far each point is from ideal relative to min duration
                below_ratio = (target_min_end - closest_point) / min_duration
                above_ratio = (earliest_after_min - target_min_end) / min_duration

                if below_ratio > 0.1 and above_ratio < 0.3:
                    # if point below min is far below, but we have a point just after min,
                    # prefer the one just after minimum
                    logger.debug(
                        "candidate point {below:.2f}s is {ratio:.1f}% below minimum, using {above:.2f}s instead",
                        below=closest_point,
                        ratio=below_ratio * 100,
                        above=earliest_after_min,
                    )
                    cut_time = earliest_after_min
                else:
                    # otherwise use the closest overall point
                    logger.debug(
                        "found balanced cut point {cut:.2f}s ({diff:.1f}s from ideal {ideal:.2f}s)",
                        cut=closest_point,
                        diff=abs(closest_point - ideal_cut_time),
                        ideal=ideal_cut_time,
                    )
                    cut_time = closest_point
            else:
                # just use the closest point
                logger.debug(
                    "found balanced cut point {cut:.2f}s ({diff:.1f}s from ideal {ideal:.2f}s)",
                    cut=closest_point,
                    diff=abs(closest_point - ideal_cut_time),
                    ideal=ideal_cut_time,
                )
                cut_time = closest_point
        elif standard_points:
            # if no candidates in the expanded range but we have standard points after min
            earliest_cut = min(standard_points)
            logger.debug(
                "no balanced candidates, using earliest valid point {cut:.2f}s", cut=earliest_cut
            )
            cut_time = earliest_cut
        else:
            # no suitable silence points at all
            logger.debug(
                "no suitable silence point found in target range. segment will extend to end or max length.",
                target=target_min_end,
            )

        # hard enforce max segment length (2x min_duration) if we're not at the end of file
        max_segment_length = segment_start + (2.0 * min_duration)
        if cut_time > max_segment_length and cut_time < total_duration:
            logger.warning(
                "enforcing maximum segment length at {max_len:.2f}s (2x min duration)",
                max_len=max_segment_length,
            )
            cut_time = max_segment_length

        # never go below 80% of minimum duration
        min_segment_length = segment_start + (0.8 * min_duration)
        if cut_time < min_segment_length:
            logger.warning(
                "enforcing minimum segment length at {min_len:.2f}s (80% of min duration)",
                min_len=min_segment_length,
            )

            # try to find a point after minimum
            points_after_min = [
                sp for sp in valid_silence_points if sp >= target_min_end and sp > last_cut_time
            ]

            if points_after_min:
                cut_time = min(points_after_min)
                logger.debug("using earliest point after minimum: {cut:.2f}s", cut=cut_time)
            else:
                # no silence points after minimum, extend to max or end
                logger.debug("no silence points after minimum, extending to maximum length or end")
                cut_time = min(max_segment_length, total_duration)

        return cut_time

    def _log_planned_segments(self, segments: List[Tuple[float, float]]) -> None:
        """
        log information about planned segments.

        args:
            segments: list of segments as (start_time, end_time) tuples
        """
        logger.info("total segments planned: {count}", count=len(segments))
        for i, (start, end) in enumerate(segments):
            logger.debug(
                "segment {idx}: start={start:.3f}s, end={end:.3f}s, duration={duration:.3f}s",
                idx=i,
                start=start,
                end=end,
                duration=end - start,
            )

    def _process_segments(self, segments: List[Tuple[float, float]]) -> List[Dict[str, Any]]:
        """
        process segments to create output files and metadata.

        args:
            segments: list of segments as (start_time, end_time) tuples

        returns:
            list of dictionaries with segment metadata
        """
        segment_metadata = []
        for idx, (start, end) in enumerate(segments):
            # skip potentially tiny segments resulting from edge cases
            if end - start < 0.1:  # skip segments shorter than 100ms
                logger.warning(
                    "skipping very short segment {idx}: {start:.2f}-{end:.2f} seconds",
                    idx=idx,
                    start=start,
                    end=end,
                )
                continue

            output_file = self._create_segment_file(idx, start, end)

            segment_metadata.append(
                {
                    "file_path": output_file,
                    "start_time": start,
                    "end_time": end,
                }
            )

        return segment_metadata

    def _create_segment_file(self, idx: int, start: float, end: float) -> str:
        """
        create a segment file using ffmpeg.

        args:
            idx: segment index
            start: start time of the segment
            end: end time of the segment

        returns:
            path to the created segment file
        """
        output_file = os.path.join(self.output_dir, f"segment_{idx:03d}.mp3")

        ffmpeg_cmd = [
            "ffmpeg",
            "-y",  # overwrite output files without asking
            "-ss",
            f"{start:.3f}",  # start time
            "-i",
            self.input_file,
            "-t",
            f"{end-start:.3f}",  # duration instead of end time
            "-vn",  # disable video recording if input might be video
            "-acodec",
            "libmp3lame",  # use mp3 encoding instead of copy
            "-ab",
            "192k",  # set bitrate
            output_file,
        ]

        segment_duration = end - start
        logger.info(
            "extracting segment {idx}: {start:.2f}-{end:.2f} sec (duration: {duration:.2f}s)",
            idx=idx,
            start=start,
            end=end,
            duration=segment_duration,
        )

        try:
            subprocess.run(ffmpeg_cmd, check=True, capture_output=True, text=True)
            logger.info("successfully extracted segment to {output}", output=output_file)
        except subprocess.CalledProcessError as e:
            logger.error("failed to extract segment: {error}", error=str(e))
            if e.stderr:
                logger.error("ffmpeg error output: {stderr}", stderr=e.stderr)
            raise

        return output_file
