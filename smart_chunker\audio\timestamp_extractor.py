import concurrent.futures
import os
import threading
import time
import xml.etree.ElementTree as ET
from typing import Any, Dict, List, Tuple

import httpx
from dotenv import load_dotenv
from google import genai
from google.genai import types
from loguru import logger

from smart_chunker.utils.retry import (
    DEFAULT_BACKOFF_FACTOR,
    DEFAULT_INITIAL_DELAY,
    DEFAULT_MAX_DELAY,
    retry_with_backoff,
)

load_dotenv()


def fix_time_format(text: str) -> str:
    """
    (legacy helper) in case the llm output still uses a non-standard time format,
    this function could be used to correct it.
    currently, we assume the llm will follow the xml prompt exactly.
    """
    return text


def clean_xml(xml_text: str) -> str:
    """
    removes any markdown code fence wrappers (e.g. "```xml" and "```")
    from the xml text so that it can be properly parsed.
    """
    # remove leading/trailing whitespace
    cleaned = xml_text.strip()
    # if the text starts with a code fence, remove it.
    if cleaned.startswith("```xml"):
        cleaned = cleaned[len("```xml") :].strip()
    # if it ends with a code fence, remove it.
    if cleaned.endswith("```"):
        cleaned = cleaned[:-3].strip()
    return cleaned


def parse_xml_to_json(xml_text: str) -> list:
    """
    parses an xml transcript into a list of word timestamp dictionaries.
    raises xml.etree.ElementTree.ParseError if parsing fails.

    expected xml format:
    <transcript>
      <word start="00:00,000" end="00:00,350">good</word>
      <word start="00:00,350" end="00:00,800">morning,</word>
      ...
    </transcript>

    returns:
        a list of dictionaries, each with keys "word", "start", and "end".
    """
    logger.debug("parsing xml transcript from lm.")

    # clean the xml to remove any code fence markers
    cleaned_xml = clean_xml(xml_text)
    # let et.fromstring raise parseerror if xml is malformed
    root = ET.fromstring(cleaned_xml)

    words = []
    for word_elem in root.findall(".//word"):
        start = word_elem.get("start")
        end = word_elem.get("end")
        text = word_elem.text.strip() if word_elem.text else ""
        if not start or not end:
            logger.warning(
                "word element missing start or end attribute: {element}",
                element=ET.tostring(word_elem, encoding="unicode"),
            )
            continue
        word_dict = {"word": text, "start": start, "end": end}
        words.append(word_dict)
    logger.debug("parsed {count} words from xml.", count=len(words))
    return words


class TimestampGenerator:
    """
    a source to generate word-level timestamps in xml format using google genai.
    the lm is instructed to produce a well-formed xml transcript per the given prompt.
    includes indefinite retry logic for file upload, api calls, and xml parsing errors.
    """

    def __init__(
        self,
        file_path: str,
        model: str = "gemini-2.5-pro-preview-05-06",
        temperature: float = 0.2,
        api_key: str = None,
        network_timeout: float = 10.0,  # default shorter timeout to make ctrl-c more responsive
    ) -> None:
        self.api_key = api_key or os.environ.get("GOOGLE_API_KEY")
        if not self.api_key:
            logger.critical("api key not provided.")
            raise ValueError(
                "api key must be provided via parameter or 'google_api_key' environment variable."
            )
        # initialize the genai client
        self.client = genai.Client(api_key=self.api_key)

        # configure httpx client with shorter timeouts to make ctrl-c more responsive
        if hasattr(self.client, "_api_client") and hasattr(
            self.client._api_client, "_httpx_client"
        ):
            request_timeout = httpx.Timeout(network_timeout, connect=3.0)
            self.client._api_client._httpx_client = httpx.Client(timeout=request_timeout)
            logger.debug(
                "configured shorter network timeout of {timeout}s for genai client",
                timeout=network_timeout,
            )

        self.file_path = file_path
        self.model = model
        self.temperature = temperature
        self.uploaded_file = None
        logger.info(
            "initialized timestampgenerator for file: {file_path}, model: {model}, temperature: {temp}",
            file_path=file_path,
            model=model,
            temp=temperature,
        )

    @retry_with_backoff(
        max_retries=-1,  # retry indefinitely
        initial_delay=2.0,
        max_delay=60.0,
        backoff_factor=2.0,
        exceptions=[  # explicitly add exception here for certainty
            httpx.WriteTimeout,
            httpx.ReadTimeout,
            httpx.ConnectTimeout,
            httpx.TimeoutException,
            ConnectionError,
            Exception,  # catch any other standard exception for retry
        ],
    )
    def _upload_file_with_retry(self) -> None:
        """internal method to upload file with retry logic."""
        logger.info("attempting to upload file: {file_path}", file_path=self.file_path)
        # note: file uploads are only supported for gemini developer api
        # if using vertex ai, this needs a different approach (e.g., gcs uri)
        uploaded = self.client.files.upload(file=self.file_path)
        if uploaded:
            self.uploaded_file = uploaded
            logger.info(
                "file uploaded successfully. file uri: {uri}, name: {name}",
                uri=self.uploaded_file.uri,
                name=self.uploaded_file.name,
            )
        else:
            # this case should ideally not happen if an exception isn't raised, but log just in case
            logger.error(
                "file upload returned none without raising an exception for file: {file_path}",
                file_path=self.file_path,
            )
            # raise an error to trigger retry
            raise ValueError("file upload returned none unexpectedly")
        # no need for explicit try/except here as the decorator handles it

    def _execute_genai_request(self, contents, system_instruction_text):
        """
        internal method to execute the genai request.
        note: retry logic is now handled by the caller (generate_timestamps).
        """
        logger.debug("sending request to genai api")

        # configure generation parameters using types.generatecontentconfig
        config = types.GenerateContentConfig(
            temperature=self.temperature,
            max_output_tokens=65536,
            system_instruction=system_instruction_text,
            response_mime_type="text/plain",  # ensure text for xml parsing
        )

        # use httpx client with custom timeout to allow keyboard interrupts
        # generate content with a shorter timeout that allows for keyboard interrupts
        try:
            # create a client with shorter timeout settings to make ctrl-c more responsive
            original_client = self.client._api_client._httpx_client
            request_timeout = httpx.Timeout(5.0, connect=3.0, read=10.0, write=3.0)
            self.client._api_client._httpx_client = httpx.Client(timeout=request_timeout)

            # generate content - errors will propagate up to the caller
            response = self.client.models.generate_content(
                model=self.model,
                contents=contents,
                config=config,
            )

            # restore original client settings
            self.client._api_client._httpx_client = original_client
        except KeyboardInterrupt:
            logger.warning("user interrupt detected during api call. terminating immediately.")
            raise
        return response

    def generate_timestamps(self) -> list:
        """
        generates word-level timestamps. retries indefinitely on specified exceptions
        during file upload, api calls, or xml parsing errors.
        """
        if self.uploaded_file is None:
            logger.debug(
                "no file uploaded yet. initiating file upload (will retry indefinitely on errors)."
            )
            # upload file handles its own retries indefinitely
            self._upload_file_with_retry()
            if self.uploaded_file is None:
                logger.critical(
                    "_upload_file_with_retry finished but self.uploaded_file is still none. unrecoverable state."
                )
                return []  # cannot proceed

        # prepare content once the file is uploaded
        content = types.Content(
            role="user",
            parts=[
                types.Part.from_uri(
                    file_uri=self.uploaded_file.uri,
                    mime_type=self.uploaded_file.mime_type,
                )
            ],
        )
        logger.debug(
            "prepared content for timestamp generation with uploaded file uri: {uri}",
            uri=self.uploaded_file.uri,
        )

        # system prompt (remains the same)
        system_instruction_text = (
            "you are an advanced speech-to-text transcription system specialized in generating precise word-level timestamps in xml format.\n\n"
            "your task is to transcribe the provided audio file and produce an xml document where each word is tagged with its exact timing information.\n\n"
            "output requirements:\n"
            "- the output must be a well-formed xml document with a root <transcript> element\n"
            "- each word should be wrapped in a <word> element with start and end time attributes\n"
            '- all timestamps must use the format "mm:ss,mmm" with minutes and seconds (zero-padded to 2 digits) and milliseconds (zero-padded to 3 digits)\n\n'
            "bad examples (do not format like these):\n\n"
            "example 1: missing zero-padding, missing end attribute, incorrect separator\n"
            "<transcript>\n"
            ' <word start="0:0,0" end="0:0,35">good</word>  <!-- bad: numbers not zero-padded -->\n'
            ' <word start="0:0,35" end="0:0,8">morning,</word>  <!-- bad: numbers not zero-padded, milliseconds not 3 digits -->\n'
            " <word>and</word>  <!-- bad: missing timestamp attributes completely -->\n"
            ' <word start="00:00.950" end="00:01.500">welcome</word>  <!-- bad: using periods instead of commas as separators -->\n'
            "</transcript>\n\n"
            "example 2: inconsistent formatting, missing end attribute, incorrect millisecond format\n"
            "<transcript>\n"
            ' <word start="0:00,000" end="0:00,350">good</word>  <!-- bad: first digit of minutes not zero-padded -->\n'
            ' <word start="00:00,350">morning,</word>  <!-- bad: missing end attribute -->\n'
            ' <word start="00:00,800" end="00:00,95">and</word>  <!-- bad: milliseconds only 2 digits instead of 3 -->\n'
            "</transcript>\n\n"
            "example 3: mixed separators\n"
            "<transcript>\n"
            ' <word start="00:00.000" end="00:00.350">good</word>  <!-- bad: using periods instead of commas -->\n'
            ' <word start="00:00,350" end="00:00.800">morning,</word>  <!-- bad: inconsistent separators (comma and period) -->\n'
            "</transcript>\n\n"
            "example 4: wrong millisecond format, improper word grouping\n"
            "<transcript>\n"
            ' <word start="00:00,00" end="00:00,35">good</word>  <!-- bad: milliseconds only 2 digits instead of 3 -->\n'
            " <words>morning, and welcome</words>  <!-- bad: using <words> tag with multiple words instead of individual <word> tags -->\n"
            "</transcript>\n\n"
            "good examples (format exactly like these):\n\n"
            "example 1: perfect formatting with proper zero-padding and comma separators\n"
            "<transcript>\n"
            ' <word start="00:00,000" end="00:00,350">good</word>  <!-- good: proper mm:ss,mmm format with zero-padding -->\n'
            ' <word start="00:00,350" end="00:00,800">morning,</word>\n'
            ' <word start="00:00,800" end="00:00,950">and</word>\n'
            ' <word start="00:00,950" end="00:01,500">welcome</word>\n'
            ' <word start="00:01,500" end="00:01,650">to</word>\n'
            ' <word start="00:01,650" end="00:01,800">our</word>\n'
            ' <word start="00:01,800" end="00:02,400">advanced</word>\n'
            ' <word start="00:02,400" end="00:03,000">transcription</word>\n'
            ' <word start="00:03,000" end="00:03,500">service.</word>\n'
            "</transcript>\n\n"
            "example 2: later in audio with proper minute marking\n"
            "<transcript>\n"
            ' <word start="03:45,200" end="03:45,400">the</word>  <!-- good: proper minutes notation (03) -->\n'
            ' <word start="03:45,400" end="03:45,700">system</word>\n'
            ' <word start="03:45,700" end="03:46,100">requires</word>\n'
            ' <word start="03:46,100" end="03:46,500">immediate</word>\n'
            ' <word start="03:46,500" end="03:46,900">attention</word>\n'
            "</transcript>\n\n"
            "example 3: double-digit minutes with proper formatting\n"
            "<transcript>\n"
            ' <word start="12:30,000" end="12:30,250">thank</word>  <!-- good: double-digit minutes properly formatted -->\n'
            ' <word start="12:30,250" end="12:30,450">you</word>\n'
            ' <word start="12:30,450" end="12:30,700">for</word>\n'
            ' <word start="12:30,700" end="12:31,000">listening</word>\n'
            ' <word start="12:31,000" end="12:31,300">to</word>\n'
            ' <word start="12:31,300" end="12:31,600">this</word>\n'
            ' <word start="12:31,600" end="12:32,000">podcast</word>\n'
            "</transcript>\n\n"
            'ensure each timestamp strictly follows the "mm:ss,mmm" format with:\n'
            "- minutes (mm): zero-padded to 2 digits (00, 05, 59, etc.)\n"
            "- seconds (ss): zero-padded to 2 digits (00, 07, 59, etc.)\n"
            "- comma separator (,): use a comma, not a period or colon\n"
            "- milliseconds (mmm): zero-padded to exactly 3 digits (000, 045, 987, etc.)"
        )

        # add explicit warning against colon for milliseconds
        system_instruction_text += (
            "\n\nIMPORTANT WARNING: a common error is using a colon instead of a comma to separate seconds and milliseconds, "
            'like "00:19:997" instead of the correct "00:19,997". '
            "ALWAYS use a comma as the separator for milliseconds, NEVER a colon. "
            "the millisecond part MUST be separated by a comma (,) from the seconds part."
        )

        logger.debug(
            "system instruction for xml timestamp generation prepared (length: {len})",
            len=len(system_instruction_text),
        )

        # --- retry loop for api call + parsing ---
        retry_count = 0
        delay = DEFAULT_INITIAL_DELAY
        while True:  # infinite loop controlled by success or non-exception error
            try:
                # step 1: execute the api request
                response = self._execute_genai_request([content], system_instruction_text)
                raw_transcript = response.text
                logger.debug(
                    "received raw response from model (attempt {attempt})", attempt=retry_count + 1
                )
                logger.debug("raw transcript: {transcript}", transcript=raw_transcript)

                # step 2: clean and parse the response
                cleaned_xml = clean_xml(raw_transcript)
                timestamp_data = parse_xml_to_json(cleaned_xml)  # this can raise parseerror

                # step 3: success! log and return
                if timestamp_data:
                    logger.info(
                        "successfully generated and parsed {count} word-level timestamps after {attempts} attempts",
                        count=len(timestamp_data),
                        attempts=retry_count + 1,
                    )
                else:
                    # this case means parsing succeeded but found no <word> elements
                    logger.warning(
                        "xml parsed successfully but contained no valid word timestamps after {attempts} attempts. response might be empty or malformed.",
                        attempts=retry_count + 1,
                    )
                return timestamp_data

            except (
                httpx.TimeoutException,
                ET.ParseError,  # explicitly catch parseerror here
                # add other specific exceptions from genai if needed, e.g., google_exceptions.apiexception
                Exception,  # catch any other general exception to retry
            ) as e:
                retry_count += 1
                current_delay = min(delay, DEFAULT_MAX_DELAY)
                logger.warning(
                    "attempt {count} failed with error: {error_type} - {error_msg}. retrying in {delay:.2f} seconds...",
                    count=retry_count,
                    error_type=type(e).__name__,
                    error_msg=str(e),
                    delay=current_delay,
                )
                # sleep in small increments to allow keyboard interrupts
                try:
                    for _ in range(int(current_delay * 10)):
                        time.sleep(0.1)  # sleep in 100ms increments
                except KeyboardInterrupt:
                    logger.critical(
                        "keyboard interrupt detected during retry wait. stopping immediately."
                    )
                    raise
                # increase delay for next potential retry
                delay = min(delay * DEFAULT_BACKOFF_FACTOR, DEFAULT_MAX_DELAY)

            except KeyboardInterrupt:
                # immediately stop on keyboard interrupt
                logger.critical("keyboard interrupt detected. stopping retries immediately.")
                raise  # re-raise to allow proper exit
            except BaseException as e:
                # catch other non-exception errors and stop retrying
                logger.critical(
                    "unrecoverable error encountered: {error}. stopping retries.", error=e
                )
                raise  # re-raise the baseexception


class ParallelTimestampProcessor:
    """
    processes multiple audio files in parallel to generate word-level timestamps.
    respects api rate limits while maximizing throughput.
    """

    def __init__(
        self,
        api_key: str = None,
        model: str = "gemini-2.5-pro-preview-05-06",
        temperature: float = 0.4,
        generator_model: str = None,  # new parameter for TimestampGenerator
        generator_temperature: float = None,  # new parameter for TimestampGenerator
        max_workers: int = 150,  # default number of parallel workers
        rate_limit_rpm: int = 150,  # rate limit in requests per minute
    ) -> None:
        self.api_key = api_key or os.environ.get("GOOGLE_API_KEY")
        if not self.api_key:
            logger.critical("api key not provided.")
            raise ValueError(
                "api key must be provided via parameter or 'google_api_key' environment variable."
            )

        self.model = model
        self.temperature = temperature

        # store generator-specific parameters, or use processor ones if not provided
        self.generator_model = generator_model or model
        self.generator_temperature = generator_temperature or temperature

        self.max_workers = max_workers

        # calculate delay between requests to respect rate limits
        # add small buffer to be safe (0.9 factor)
        self.request_delay = (60.0 / rate_limit_rpm) * 0.9

        logger.info(
            "initialized parallel timestamp processor with {workers} workers, {rpm} rpm, model: {model}, generator model: {gen_model}",
            workers=max_workers,
            rpm=rate_limit_rpm,
            model=model,
            gen_model=self.generator_model,
        )

        # track the last request time to enforce rate limiting
        self.last_request_time = 0
        self._lock = threading.Lock()  # for thread-safe time tracking

    def _process_file(self, file_path: str) -> Tuple[str, List[Dict[str, Any]]]:
        """
        process a single file with rate limiting.
        returns a tuple of (file_path, timestamp_data)
        """
        # apply rate limiting
        with self._lock:
            current_time = time.time()
            time_since_last_request = current_time - self.last_request_time

            if time_since_last_request < self.request_delay:
                sleep_time = self.request_delay - time_since_last_request
                logger.debug(
                    "rate limiting: sleeping for {time:.3f}s before processing {file}",
                    time=sleep_time,
                    file=os.path.basename(file_path),
                )
                time.sleep(sleep_time)

            self.last_request_time = time.time()

        # create a timestamp generator for this file using generator-specific parameters
        generator = TimestampGenerator(
            file_path=file_path,
            model=self.generator_model,  # use generator-specific model
            temperature=self.generator_temperature,  # use generator-specific temperature
            api_key=self.api_key,
        )

        # generate timestamps
        try:
            timestamp_data = generator.generate_timestamps()
            return file_path, timestamp_data
        except Exception as e:
            logger.error("failed to process file {file}: {error}", file=file_path, error=str(e))
            return file_path, []

    def process_files(self, file_paths: List[str]) -> Dict[str, List[Dict[str, Any]]]:
        """
        process multiple files in parallel.

        args:
            file_paths: list of paths to audio files

        returns:
            dictionary mapping file paths to their timestamp data
        """
        results = {}
        total_files = len(file_paths)

        logger.info(
            "starting parallel processing of {count} files with {workers} workers",
            count=total_files,
            workers=min(self.max_workers, total_files),
        )

        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # submit all tasks
            future_to_file = {
                executor.submit(self._process_file, file_path): file_path
                for file_path in file_paths
            }

            # collect results as they complete
            for i, future in enumerate(concurrent.futures.as_completed(future_to_file), 1):
                file_path = future_to_file[future]
                try:
                    file_path, timestamp_data = future.result()
                    results[file_path] = timestamp_data
                    logger.info(
                        "completed {i}/{total} - file: {file} with {count} timestamps",
                        i=i,
                        total=total_files,
                        file=os.path.basename(file_path),
                        count=len(timestamp_data),
                    )
                except Exception as e:
                    logger.error(
                        "exception processing file {file}: {error_type} - {error}",
                        file=os.path.basename(file_path),
                        error_type=type(e).__name__,
                        error=str(e),
                    )
                    results[file_path] = []

        logger.info("completed parallel processing of {count} files", count=total_files)
        return results

    def process_directory(
        self, directory_path: str, file_extension: str = ".wav"
    ) -> Dict[str, List[Dict[str, Any]]]:
        """
        processes all audio files with the specified extension in the given directory.
        """
        logger.info(
            "processing all {ext} files in directory: {dir}",
            ext=file_extension,
            dir=directory_path,
        )
        file_paths = []
        for file in os.listdir(directory_path):
            if file.endswith(file_extension):
                file_paths.append(os.path.join(directory_path, file))

        if not file_paths:
            logger.warning(
                "no {ext} files found in directory: {dir}",
                ext=file_extension,
                dir=directory_path,
            )
            return {}

        logger.info("found {count} files to process", count=len(file_paths))
        return self.process_files(file_paths)

    def process_segments(self, segments: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """
        processes a list of segment metadata dicts with file_path, start_time, end_time.
        returns a list of lists, where each inner list contains the timestamped words for a segment.
        """
        logger.info(
            "processing {count} audio segments with timestamp extraction",
            count=len(segments),
        )

        # extract file paths from segments
        file_paths = [segment["file_path"] for segment in segments]

        # process the files
        results_dict = self.process_files(file_paths)

        # convert dict results to ordered list matching the input segments
        segment_timestamps_list = []
        for segment in segments:
            file_path = segment["file_path"]
            if file_path in results_dict:
                segment_timestamps_list.append(results_dict[file_path])
            else:
                logger.warning(
                    "no timestamps generated for segment: {file_path}",
                    file_path=file_path,
                )
                segment_timestamps_list.append([])

        return segment_timestamps_list
