#!/usr/bin/env python3
"""
cli interface for the smart chunker package
"""
import argparse
import sys
from pathlib import Path

from smart_chunker.utils.logging import setup_logger

logger = setup_logger()


def main():
    """main cli entry point"""
    parser = argparse.ArgumentParser(
        description="smart chunker - audio chunking and transcript alignment"
    )
    parser.add_argument("--input", required=True, help="input audio file path")
    parser.add_argument("--output", required=True, help="output directory for chunks")
    parser.add_argument(
        "--config",
        default="smart_chunker/config/run_config.json",
        help="path to config file",
    )

    args = parser.parse_args()

    if not Path(args.input).exists():
        logger.error(f"input file {args.input} not found")
        sys.exit(1)

    # run the pipeline
    # this would call the actual pipeline components
    logger.info(f"processing {args.input} and saving to {args.output}")

    return 0


if __name__ == "__main__":
    sys.exit(main())
