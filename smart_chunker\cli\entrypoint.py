"""command-line entrypoint for smart chunker.

usage::

    python -m smart_chunker.cli.entrypoint --config smart_chunker/config/run_config.json
"""

from __future__ import annotations

import argparse
import sys
from pathlib import Path

from loguru import logger

from smart_chunker.pipeline.main import smartchunkerpipeline


def _build_parser() -> argparse.ArgumentParser:  # noqa: d401
    parser = argparse.ArgumentParser(description="smart chunker processing pipeline")
    parser.add_argument(
        "--config",
        type=Path,
        default=Path("smart_chunker/config/run_config.json"),
        help="path to the run configuration json file",
    )
    return parser


def main(argv: list[str] | None = None) -> None:  # noqa: d401
    """parse arguments and kick off the pipeline."""

    argv = argv if argv is not None else sys.argv[1:]
    parser = _build_parser()
    args = parser.parse_args(argv)

    pipeline = smartchunkerpipeline(str(args.config))

    try:
        pipeline.run()
    except KeyboardInterrupt:  # pragma: no cover
        logger.warning("interrupted by user (ctrl-c). exiting immediately.")
        sys.exit(1)


if __name__ == "__main__":
    main()
