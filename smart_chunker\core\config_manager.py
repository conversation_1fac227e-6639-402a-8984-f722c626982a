"""smart chunker configuration manager.

this module provides utilities to load and validate the runtime configuration
for the smart chunker pipeline.
"""

from __future__ import annotations

import json
from pathlib import Path

from loguru import logger


class runconfig:  # pylint: disable=too-few-public-methods
    """simple wrapper around a configuration dictionary."""

    def __init__(self, data: dict):  # noqa: d401
        """store raw config data."""
        self.data = data

    def __getitem__(self, item):
        return self.data[item]

    def get(self, item, default=None):
        return self.data.get(item, default)

    @property
    def raw(self) -> dict:  # noqa: d401
        """return the underlying dictionary."""
        return self.data

    # additional helpers (e.g. schema validation) can be added later


def load_config(path: str | Path) -> runconfig:  # noqa: d401
    """load a json configuration file.

    this function replicates the original ``load_config`` in ``run.py`` but lives
    in its own module so that the logic can be reused by different entrypoints
    (cli, tests, etc.).
    """

    path = Path(path)
    logger.info("loading configuration from: {}", path)

    try:
        with path.open("r", encoding="utf-8") as fp:
            data = json.load(fp)
        logger.info("configuration loaded successfully")
        return runconfig(data)

    except FileNotFoundError:  # pragma: no cover – obvious error path
        logger.critical("configuration file not found: {}", path)
        raise

    except json.JSONDecodeError as err:  # pragma: no cover – should be unit-tested separately
        logger.critical("error decoding json from configuration file {}: {}", path, err)
        raise

    except Exception as err:  # noqa: BLE001
        logger.critical("unexpected error loading configuration: {}", err)
        raise
