"""filesystem helper utilities for smart chunker.

these helpers were extracted from ``run.py`` to follow the single-responsibility
principle and to improve testability.
"""

from __future__ import annotations

import json
import os
from pathlib import Path
from typing import List

from loguru import logger


def ensure_output_directory(base_dir: str | os.PathLike[str], run_id: str) -> str:
    """create ``<base_dir>/<run_id>`` if it does not exist and return its path."""
    output_dir = Path(base_dir) / run_id
    if not output_dir.exists():
        output_dir.mkdir(parents=True, exist_ok=True)
        logger.info("created output directory for run: {}", output_dir)
    return str(output_dir)


def save_translated_chunks_to_file(
    translated_chunks: List[str],
    input_base: str,
    base_output_dir: str | os.PathLike[str],
    run_id: str,
    target_language: str | None = None,
) -> str | None:
    """persist translated chunks as ``*.translated.<lang>.json`` and return the path."""

    output_dir = ensure_output_directory(base_output_dir, run_id)
    lang_part = f".{target_language}" if target_language else ""
    output_filename = Path(output_dir) / f"{input_base}.translated{lang_part}.json"

    try:
        with output_filename.open("w", encoding="utf-8") as fp:
            json.dump(translated_chunks, fp, ensure_ascii=False, separators=(",", ":"))
        logger.info("translated chunks saved as: {}", output_filename)
        return str(output_filename)
    except IOError as err:  # pragma: no cover
        logger.error("failed to save translated chunks file {}: {}", output_filename, err)
        return None
