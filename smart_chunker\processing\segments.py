"""audio segmentation and timestamp extraction logic.

this module houses the former ``segment_audio_and_extract_timestamps`` function
originally located in ``run.py``. keeping it here decouples audio processing
from the monolithic script and allows independent testing.
"""

from __future__ import annotations

import os
import subprocess
import tempfile
from typing import Any, Dict, List, Tuple

from loguru import logger

from smart_chunker.audio import ParallelTimestampProcessor, SilenceDetector
from smart_chunker.transcript import adjust_timestamps_with_segments

__all__ = [
    "segment_audio_and_extract_timestamps",
]


def segment_audio_and_extract_timestamps(
    original_file: str,
    config: Dict[str, Any],
    run_id: str,
    max_parallel_workers: int = 5,
    rate_limit_rpm: int = 150,
    timestamp_model: str = "gemini-2.5-pro-preview-05-06",
    timestamp_temperature: float = 0.2,
    processor_model: str = "gemini-2.5-pro-preview-05-06",
    processor_temperature: float = 0.4,
):
    """split ``original_file`` into speech segments and extract word timestamps.

    the logic is a direct lift-and-shift from the legacy ``run.py`` implementation.
    over time this will be refactored into smaller helpers, but for now we retain
    identical behaviour to avoid regressions.

    returns
    -------
    tuple | None
        (segment_metadata_list, all_adjusted_timestamps, temp_dir_obj)
    """

    logger.info("step 0: starting audio segmentation with new silencedetector logic.")

    input_cfg = config.get("input_processing", {})
    sd_cfg = input_cfg.get("silence_detection", {})

    min_segment_duration_seconds = input_cfg.get("min_segment_duration_seconds", 60)
    use_temp_dir_for_segments = input_cfg.get("use_temporary_directory_for_segments", True)

    # determine where to save segments
    segment_output_base_dir = config.get("output", {}).get("base_directory", "data/output")

    temp_dir_obj: tempfile.TemporaryDirectory[str] | None = None

    try:
        # create temp directory if needed
        if use_temp_dir_for_segments:
            temp_dir_obj = tempfile.TemporaryDirectory(prefix="sc_segments_")
            actual_segment_output_dir = temp_dir_obj.name
            logger.info(
                "using temporary directory for segments: {dir}", dir=actual_segment_output_dir
            )
        else:
            actual_segment_output_dir = os.path.join(
                segment_output_base_dir, run_id, "audio_segments"
            )
            os.makedirs(actual_segment_output_dir, exist_ok=True)
            logger.info("using output directory for segments: {dir}", dir=actual_segment_output_dir)

        # initialise silence detector
        detector = SilenceDetector(
            file_path=original_file,
            method=sd_cfg.get("method", "ffmpeg"),
            min_silence_len=sd_cfg.get("min_silence_len_ms", 500),
            abs_thresh=sd_cfg.get("abs_threshold_db", -30.0),
            rel_thresh=None,
            log_level=config.get("logging", {}).get("level", "INFO").upper(),
            downsample_rate=sd_cfg.get("ffmpeg_downsample_rate"),
        )
        logger.info("silencedetector initialized for file: {file}", file=original_file)
        logger.info("total audio duration: {dur_fmt}", dur_fmt=detector.formatted_duration)

        # detect speech segments with padding
        speech_padding_ms = sd_cfg.get("speech_segment_padding_ms", 200)
        raw_speech_segments_times: List[Tuple[float, float]] = detector.get_speech_segments(
            padding_ms=speech_padding_ms
        )
        if not raw_speech_segments_times:
            logger.error("no speech segments detected by silencedetector. cannot proceed.")
            return None, None, None

        logger.info("detected {count} raw speech segments.", count=len(raw_speech_segments_times))

        # merge speech segments according to min duration and silence bridging
        final_segment_tuples_for_cutting: List[Tuple[float, float]] = []
        max_silence_to_bridge_s = sd_cfg.get("max_silence_to_bridge_seconds", 2.0)

        current_merged_start = -1.0
        current_merged_end = -1.0

        for speech_start, speech_end in raw_speech_segments_times:
            if current_merged_start == -1.0:
                current_merged_start = speech_start
                current_merged_end = speech_end
            else:
                silence_duration_between = max(0.0, speech_start - current_merged_end)
                current_potential_duration = current_merged_end - current_merged_start

                if (
                    current_potential_duration < min_segment_duration_seconds
                    and silence_duration_between <= max_silence_to_bridge_s
                ):
                    current_merged_end = speech_end  # merge
                else:
                    final_segment_tuples_for_cutting.append(
                        (current_merged_start, current_merged_end)
                    )
                    current_merged_start = speech_start
                    current_merged_end = speech_end

        if current_merged_start != -1.0:
            final_segment_tuples_for_cutting.append((current_merged_start, current_merged_end))

        logger.info(
            "processed into {count} final segments for cutting after merging logic.",
            count=len(final_segment_tuples_for_cutting),
        )

        # extract each segment with ffmpeg
        segment_metadata_list: List[Dict[str, Any]] = []
        for idx, (start_s, end_s) in enumerate(final_segment_tuples_for_cutting):
            segment_duration_s = end_s - start_s
            if segment_duration_s < 0.1:
                logger.warning(
                    "skipping very short segment {idx} (duration {dur:.2f}s)",
                    idx=idx,
                    dur=segment_duration_s,
                )
                continue

            output_segment_filename = f"segment_{idx:03d}.mp3"
            output_segment_filepath = os.path.join(
                actual_segment_output_dir, output_segment_filename
            )

            ffmpeg_cmd = [
                "ffmpeg",
                "-y",
                "-ss",
                f"{start_s:.3f}",
                "-i",
                original_file,
                "-t",
                f"{segment_duration_s:.3f}",
                "-vn",
                "-acodec",
                "libmp3lame",
                "-ab",
                "192k",
                output_segment_filepath,
            ]
            logger.info(
                "extracting segment {idx}: {start:.2f}s - {end:.2f}s (duration: {dur:.2f}s) to {path}",
                idx=idx,
                start=start_s,
                end=end_s,
                dur=segment_duration_s,
                path=output_segment_filepath,
            )
            try:
                subprocess.run(ffmpeg_cmd, check=True, capture_output=True, text=True)
                segment_metadata_list.append(
                    {
                        "file_path": output_segment_filepath,
                        "start_time": start_s,
                        "end_time": end_s,
                    }
                )
            except subprocess.CalledProcessError as err:  # noqa: BLE001
                logger.error("failed to extract segment {idx}: {error}", idx=idx, error=str(err))
                if err.stderr:
                    logger.error("ffmpeg error output: {stderr}", stderr=err.stderr)

        if not segment_metadata_list:
            logger.error("no audio segments were successfully created. exiting.")
            if temp_dir_obj:
                temp_dir_obj.cleanup()
            return None, None, None

        logger.info(
            "step 0: audio split into {count} segments using silencedetector.",
            count=len(segment_metadata_list),
        )

        # step 1: parallel timestamp extraction
        logger.info(
            "step 1: starting parallel word-level extraction for segments with {workers} workers at {rpm} rpm.",
            workers=max_parallel_workers,
            rpm=rate_limit_rpm,
        )

        valid_segments = [s for s in segment_metadata_list if os.path.exists(s["file_path"])]
        if not valid_segments:
            logger.error("no valid segments found. cannot proceed.")
            if temp_dir_obj:
                temp_dir_obj.cleanup()
            return None, None, None

        timestamp_processor = ParallelTimestampProcessor(
            max_workers=max_parallel_workers,
            rate_limit_rpm=rate_limit_rpm,
            model=processor_model,
            temperature=processor_temperature,
            generator_model=timestamp_model,
            generator_temperature=timestamp_temperature,
        )

        segment_timestamps_list = timestamp_processor.process_segments(valid_segments)
        if not segment_timestamps_list:
            logger.error("no segments could be processed. exiting.")
            if temp_dir_obj:
                temp_dir_obj.cleanup()
            return None, None, None

        logger.info("adjusting timestamps based on segment offsets…")
        all_adjusted_timestamps = adjust_timestamps_with_segments(
            segment_timestamps_list, valid_segments
        )
        logger.info(
            "step 1: extracted and adjusted timestamps for {count} segments with {words} total words.",
            count=len(all_adjusted_timestamps),
            words=sum(len(seg) for seg in all_adjusted_timestamps),
        )

        return segment_metadata_list, all_adjusted_timestamps, temp_dir_obj

    except Exception as err:  # noqa: BLE001
        logger.exception("error during audio processing: {}", str(err))
        if temp_dir_obj:
            temp_dir_obj.cleanup()
        return None, None, None
