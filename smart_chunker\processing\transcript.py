"""transcript processing helpers.

contains functions for concatenation, segmentation, timestamp mapping, and srt
export that used to live inside ``run.py``.
"""

from __future__ import annotations

import json
import os
from typing import Any, Dict, List

from loguru import logger

from smart_chunker.core.file_ops import ensure_output_directory
from smart_chunker.transcript import (
    JsonTextConcatenation,
    TranscriptionChunker,
    TimestampMapper,
    export_srt,
)

__all__ = [
    "concatenate_transcript",
    "segment_transcript",
    "map_timestamps_and_export_srt",
]


def concatenate_transcript(all_adjusted_timestamps: List[List[Dict[str, Any]]]):  # noqa: d401
    """flatten timestamp lists and build full transcript dict."""
    logger.info("step 2: concatenating all timestamps into a full transcript…")

    flat_timestamps = [item for segment in all_adjusted_timestamps for item in segment]

    concatenator = JsonTextConcatenation(flat_timestamps)
    concatenated_text = concatenator.concatenate_words()

    full_transcript = {"words": flat_timestamps, "text": concatenated_text}

    logger.info(
        "step 2: full transcript created with {count} words.",
        count=len(full_transcript["words"]),
    )
    return full_transcript


def segment_transcript(
    full_transcript: Dict[str, Any],
    input_base: str,
    base_output_dir: str,
    run_id: str,
    *,
    model: str = "gemini-2.5-pro-preview-05-06",
    temperature: float = 0.2,
):
    """segment the full transcript text into manageable chunks and save json."""

    logger.info("step 3: segmenting full transcript into manageable chunks…")

    word_count = len(full_transcript.get("words", []))
    text_length = len(full_transcript.get("text", ""))
    logger.info(
        "transcript data: {words} words, {chars} characters", words=word_count, chars=text_length
    )

    chunker = TranscriptionChunker(model=model, temperature=temperature)
    text_to_segment = full_transcript["text"]

    segmented_json = chunker.segment_text(text_to_segment)
    chunks_output = {"chunks": json.loads(segmented_json)}

    if not chunks_output["chunks"]:
        logger.error("segmentation failed; no chunks created")
        return None, None

    chunk_count = len(chunks_output["chunks"])
    avg_len = sum(len(c) for c in chunks_output["chunks"]) / chunk_count if chunk_count else 0
    logger.info(
        "step 3: transcript segmented into {count} chunks. avg length: {avg:.1f} chars",
        count=chunk_count,
        avg=avg_len,
    )

    output_dir = ensure_output_directory(base_output_dir, run_id)
    segmented_chunks_filename = os.path.join(output_dir, f"{input_base}.chunks.json")
    try:
        with open(segmented_chunks_filename, "w", encoding="utf-8") as fp:
            json.dump(chunks_output["chunks"], fp, ensure_ascii=False, separators=(",", ":"))
        logger.info("segmented chunks saved as: {}", segmented_chunks_filename)
    except IOError as err:  # pragma: no cover
        logger.error("failed to save segmented chunks file {}: {}", segmented_chunks_filename, err)
        segmented_chunks_filename = None

    return chunks_output, segmented_chunks_filename


def map_timestamps_and_export_srt(
    all_adjusted_timestamps: List[List[Dict[str, Any]]],
    chunks_output: Dict[str, Any] | List[str],
    input_base: str,
    base_output_dir: str,
    run_id: str,
):
    """map timestamps onto segments and export original srt file."""

    logger.info("step 4: mapping timestamps to create srt-compatible entries…")

    mapper = TimestampMapper()
    text_chunks: List[str] = (
        chunks_output if isinstance(chunks_output, list) else chunks_output.get("chunks", [])
    )

    flat_timestamps = [item for segment in all_adjusted_timestamps for item in segment]
    mapping_result = mapper.map_timestamps(flat_timestamps, text_chunks)
    if not mapping_result:
        logger.error("timestamp mapping failed; no entries created")
        return None

    logger.info("step 4: mapped {count} entries for srt export.", count=len(mapping_result))

    srt_content = export_srt(mapping_result)
    output_dir = ensure_output_directory(base_output_dir, run_id)
    srt_filename = os.path.join(output_dir, f"{input_base}.srt")

    try:
        with open(srt_filename, "w", encoding="utf-8") as fp:
            fp.write(srt_content)
        logger.info("srt file saved as: {}", srt_filename)
    except IOError as err:  # pragma: no cover
        logger.error("failed to save srt file {}: {}", srt_filename, err)

    return {"entries": mapping_result}
