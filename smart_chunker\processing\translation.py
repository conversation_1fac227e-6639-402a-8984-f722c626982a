"""translation processing helpers.

includes translation request analysis, segment translation, and translated srt
export functions previously embedded in ``run.py``.
"""

from __future__ import annotations

import json
import os
from typing import Any, Dict, List

from loguru import logger

from smart_chunker.core.file_ops import ensure_output_directory
from smart_chunker.transcript import export_srt
from smart_chunker.translation import ParallelTranslationProcessor, TranslationRequestAnalyzer

__all__ = [
    "analyze_translation_request",
    "translate_transcript_chunks",
    "save_translated_srt",
]


def analyze_translation_request(
    full_transcript: Dict[str, Any],
    source_language: str,
    target_language: str,
    *,
    model: str = "gemini-2.5-pro-preview-05-06",
    temperature: float = 0.2,
) -> Dict[str, Any]:
    """infer translation style markers from the full transcript text."""

    logger.info(
        "step 5: analyzing translation request from {source} to {target}…",
        source=source_language,
        target=target_language,
    )

    try:
        analyzer = TranslationRequestAnalyzer(model=model, temperature=temperature)
        request_payload = {
            "source_language": source_language,
            "target_language": target_language,
            "full_text": full_transcript.get("text", ""),
        }
        style_request_json = analyzer.analyze_request(request_payload)
        if not style_request_json:
            logger.warning("no style profile produced; using default style")
            return {}

        try:
            parsed = json.loads(style_request_json)
            logger.info(
                "style profile created with {count} characteristics.",
                count=len(parsed.get("style_markers", {})),
            )
            return parsed
        except json.JSONDecodeError:  # pragma: no cover – depends on model output
            logger.warning(
                "style profile json invalid; ignoring. raw: {raw}", raw=style_request_json
            )
            return {}

    except Exception as err:  # noqa: BLE001
        logger.exception("error during translation request analysis: {}", err)
        return {}


def translate_transcript_chunks(
    chunks_output: Dict[str, Any] | List[str],
    full_transcript: Dict[str, Any],
    source_language: str,
    target_language: str,
    style_request: Dict[str, Any] | None,
    *,
    max_workers: int = 5,
    rate_limit_rpm: int = 150,
    model: str = "gemini-2.5-pro-preview-05-06",
    temperature: float = 0.2,
) -> List[str]:
    """translate each chunk of the transcript in parallel."""

    logger.info(
        "step 6: translating all segments from {source} to {target} with {workers} workers at {rpm} rpm…",
        source=source_language,
        target=target_language,
        workers=max_workers,
        rpm=rate_limit_rpm,
    )

    try:
        translator = ParallelTranslationProcessor(
            max_workers=max_workers,
            rate_limit_rpm=rate_limit_rpm,
            model=model,
            temperature=temperature,
        )

        segments_to_translate: List[str] = (
            chunks_output if isinstance(chunks_output, list) else chunks_output.get("chunks", [])
        )
        if not segments_to_translate:
            logger.error("no chunks found to translate")
            return []

        translation_results = translator.translate_batch(
            segments_to_translate=segments_to_translate,
            full_text=full_transcript.get("text", ""),
            current_language=source_language,
            target_language=target_language,
            style_request=style_request or {},
        )

        if not translation_results:
            logger.error("translation failed; no chunks were translated")
            return []

        sorted_ids = sorted(translation_results.keys(), key=lambda x: int(x) if x.isdigit() else x)
        translated_chunks = [
            translation_results[seg_id].get("translated_segment", "") for seg_id in sorted_ids
        ]
        logger.info("successfully translated {count} chunks.", count=len(translated_chunks))
        return translated_chunks

    except Exception as err:  # noqa: BLE001
        logger.exception("error during translation: {}", err)
        return []


def save_translated_srt(
    mapping_result: Dict[str, Any] | None,
    translated_chunks: List[str] | None,
    input_base: str,
    base_output_dir: str,
    run_id: str,
    target_language: str | None = None,
) -> None:
    """create and save translated srt file from mapped entries and translated text."""

    logger.info("step 7: creating translated srt file…")

    if not mapping_result or not translated_chunks:
        logger.error("cannot create translated srt; missing mapping result or translations")
        return

    translated_entries = []
    for idx, entry in enumerate(mapping_result.get("entries", [])):
        new_entry = entry.copy()
        if idx < len(translated_chunks):
            new_entry["chunk_text"] = translated_chunks[idx]
        translated_entries.append(new_entry)

    translated_srt = export_srt(translated_entries)
    output_dir = ensure_output_directory(base_output_dir, run_id)
    lang_part = f".{target_language}" if target_language else ""
    translated_srt_filename = os.path.join(output_dir, f"{input_base}.translated{lang_part}.srt")

    try:
        with open(translated_srt_filename, "w", encoding="utf-8") as fp:
            fp.write(translated_srt)
        logger.info("translated srt file saved as: {}", translated_srt_filename)
    except IOError as err:  # pragma: no cover
        logger.error("failed to save translated srt file {}: {}", translated_srt_filename, err)
