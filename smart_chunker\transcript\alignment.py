import re
import string
from typing import Any, Dict, List, Optional, Tuple

from loguru import logger

# --- basic helper functions ---


def normalize_word(word: str) -> str:
    """
    normalize a word by converting to lower-case and stripping trailing punctuation and whitespace.
    """
    return re.sub(r"[^\w]+$", "", word.lower().strip(string.punctuation + " \n\t"))


def _parse_hours_minutes_seconds(parts: List[str]) -> Tuple[int, int, int]:
    if len(parts) == 3:  # hh:mm:ss format
        return int(parts[0]), int(parts[1]), 0
    elif len(parts) == 2:  # mm:ss format
        return 0, int(parts[0]), 0
    elif len(parts) == 1:  # ss format
        return 0, 0, 0
    else:
        raise ValueError("unexpected number of timestamp parts: " + str(len(parts)))


def _parse_seconds_milliseconds(seconds_part: str) -> Tuple[int, int]:
    if "," in seconds_part:
        sec_ms = seconds_part.split(",")
        if len(sec_ms) == 2:
            return int(sec_ms[0]), int(sec_ms[1])
    if ":" in seconds_part:
        sec_ms = seconds_part.split(":")
        if len(sec_ms) == 2:
            logger.warning(
                f"timestamp uses colon instead of comma for milliseconds: {seconds_part}. attempting parse."
            )
            try:
                seconds = int(sec_ms[0])
                milliseconds = int(sec_ms[1])
                # check if it looks like milliseconds (3 digits and in valid range)
                if len(sec_ms[1]) == 3 and 0 <= milliseconds < 1000:
                    logger.warning(
                        f"parsed colon-separated milliseconds: {seconds}s {milliseconds}ms"
                    )
                    return seconds, milliseconds
            except ValueError:
                logger.warning(f"could not parse colon-separated parts as integers: {seconds_part}")
                # fallback to the general method below
    # add explicit handling for underscore separator
    if "_" in seconds_part:
        sec_ms = seconds_part.split("_")
        if len(sec_ms) == 2:
            logger.warning(
                f"timestamp uses underscore instead of comma for milliseconds: {seconds_part}"
            )
            try:
                seconds = int(sec_ms[0])
                milliseconds = int(sec_ms[1])
                logger.debug(f"parsed underscore format: {seconds}s {milliseconds}ms")
                return seconds, milliseconds
            except ValueError:
                logger.warning(
                    f"could not parse underscore-separated parts as integers: {seconds_part}"
                )
                # fallback to the general method below
    try:
        clean_part = seconds_part.replace(",", ".").replace(":", ".").replace("_", ".")
        seconds_float = float(clean_part)
        seconds = int(seconds_float)
        milliseconds = int(round((seconds_float - seconds) * 1000))
        logger.warning(f"parsed seconds.milliseconds fallback -> {seconds}.{milliseconds:03d}")
        return seconds, milliseconds
    except ValueError:
        raise ValueError(f"timestamp seconds/milliseconds part malformed: {seconds_part}")


def _calculate_total_seconds(hours: int, minutes: int, seconds: int, milliseconds: int) -> float:
    return hours * 3600 + minutes * 60 + seconds + milliseconds / 1000.0


def parse_srt_timestamp(srt_time: str) -> float:
    try:
        # clean the entire srt_time string first to remove 's' that might be attached to digits
        # e.g., "01:02s:03,456" -> "01:02:03,456"; "00:18s,110" -> "00:18,110"
        cleaned_srt_time = re.sub(r"(\d+)s", r"\1", srt_time)
        if srt_time != cleaned_srt_time:
            logger.trace(
                f"original srt_time: '{srt_time}', cleaned by removing 's': '{cleaned_srt_time}'"
            )

        parts = cleaned_srt_time.split(":")
        hours, minutes, _ = _parse_hours_minutes_seconds(parts)
        seconds_part = parts[-1]
        seconds, milliseconds = _parse_seconds_milliseconds(seconds_part)
        total_seconds = _calculate_total_seconds(hours, minutes, seconds, milliseconds)
        logger.trace(f"parsed '{srt_time}' -> {total_seconds:.3f}s")
        return total_seconds
    except ValueError as e:
        logger.error(f"error parsing srt timestamp '{srt_time}': {e}")
        raise ValueError(f"invalid srt timestamp format or value: {srt_time}") from e
    except Exception as e:
        logger.exception(f"unexpected error parsing srt timestamp '{srt_time}': {e}")
        raise ValueError(f"unexpected error parsing timestamp: {srt_time}") from e


def format_timestamp(seconds: float) -> str:
    if seconds < 0:
        logger.warning(
            f"received negative seconds value ({seconds:.3f}), formatting as 00:00:00,000."
        )
        seconds = 0
    try:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        milliseconds = int(round((seconds - int(seconds)) * 1000))
        if milliseconds >= 1000:
            secs += 1
            milliseconds = 0
            if secs >= 60:
                minutes += 1
                secs = 0
                if minutes >= 60:
                    hours += 1
                    minutes = 0
        formatted = f"{hours:02}:{minutes:02}:{secs:02},{milliseconds:03}"
        logger.trace(f"formatted {seconds:.3f} s as '{formatted}'")
        return formatted
    except Exception as e:
        logger.exception(f"unexpected error formatting {seconds:.3f} s: {e}")
        return "00:00:00,000"


def adjust_timestamps(
    timestamps: List[Dict[str, Any]], offset_seconds: float
) -> List[Dict[str, Any]]:
    if offset_seconds <= 0:
        logger.debug(
            f"offset is zero or negative ({offset_seconds:.3f}s), returning original timestamps."
        )
        return timestamps
    adjusted_timestamps = []
    logger.debug(f"adjusting {len(timestamps)} timestamps by {offset_seconds:.3f} seconds.")
    for ts in timestamps:
        try:
            adjusted_ts = ts.copy()
            if "start" not in adjusted_ts or "end" not in adjusted_ts:
                logger.warning(f"timestamp missing 'start' or 'end': {ts}. skipping adjustment.")
                adjusted_timestamps.append(adjusted_ts)
                continue
            start_sec = parse_srt_timestamp(adjusted_ts["start"])
            end_sec = parse_srt_timestamp(adjusted_ts["end"])
            new_start_sec = start_sec + offset_seconds
            new_end_sec = end_sec + offset_seconds
            adjusted_ts["start"] = format_timestamp(new_start_sec)
            adjusted_ts["end"] = format_timestamp(new_end_sec)
            adjusted_timestamps.append(adjusted_ts)
            logger.trace(
                f"adjusted '{adjusted_ts.get('word','n/a')}': {ts['start']} -> {adjusted_ts['start']}"
            )
        except KeyError as e:
            logger.warning(f"missing key {e} in {ts}. skipping adjustment.")
            adjusted_timestamps.append(ts)
        except ValueError as e:
            logger.warning(
                f"error processing timestamp for '{ts.get('word','n/a')}': {e}. skipping adjustment."
            )
            adjusted_timestamps.append(ts)
        except Exception as e:
            logger.exception(
                f"unexpected error for '{ts.get('word','n/a')}': {e}. skipping adjustment."
            )
            adjusted_timestamps.append(ts)
    logger.debug("finished adjusting timestamps.")
    return adjusted_timestamps


def adjust_timestamps_with_segments(
    segment_timestamps_list: List[List[Dict[str, Any]]], segments_metadata: List[Dict[str, Any]]
) -> List[List[Dict[str, Any]]]:
    """
    adjust timestamps for multiple segments based on each segment's start time.
    each segment has its own offset (start_time) that needs to be applied to all timestamps in that segment.

    args:
        segment_timestamps_list: list of lists, where each inner list contains timestamps for one segment
        segments_metadata: list of dictionaries with segment metadata including 'start_time'

    returns:
        list of lists with adjusted timestamps
    """
    if len(segment_timestamps_list) != len(segments_metadata):
        logger.warning(
            f"mismatch between segment_timestamps_list ({len(segment_timestamps_list)}) "
            f"and segments_metadata ({len(segments_metadata)})"
        )

    all_adjusted_timestamps = []

    for i, (timestamps, segment) in enumerate(zip(segment_timestamps_list, segments_metadata)):
        try:
            offset_seconds = segment.get("start_time", 0.0)
            logger.debug(f"adjusting segment {i} timestamps with offset {offset_seconds:.3f}s")
            adjusted = adjust_timestamps(timestamps, offset_seconds)
            all_adjusted_timestamps.append(adjusted)
        except Exception as e:
            logger.exception(f"error adjusting timestamps for segment {i}: {e}")
            # add empty list to maintain segment count
            all_adjusted_timestamps.append([])

    return all_adjusted_timestamps


def _convert_timestamp_to_float(
    timestamp_value, timestamp_type: str, index: int
) -> Optional[float]:
    if isinstance(timestamp_value, str):
        return parse_srt_timestamp(timestamp_value)
    elif isinstance(timestamp_value, (int, float)):
        return float(timestamp_value)
    else:
        logger.error(
            f"invalid type for '{timestamp_type}' timestamp in chunk {index}: {type(timestamp_value)}"
        )
        return None


def export_srt(mapped_chunks: List[Dict[str, Any]]) -> str:
    srt_entries = []
    logger.info(f"exporting {len(mapped_chunks)} srt entries")
    for index, chunk in enumerate(mapped_chunks, start=1):
        try:
            start_time_num = _convert_timestamp_to_float(chunk["start"], "start", index)
            if start_time_num is None:
                continue
            end_time_num = _convert_timestamp_to_float(chunk["end"], "end", index)
            if end_time_num is None:
                continue
            if end_time_num < start_time_num:
                logger.warning(
                    f"chunk {index}: end time {end_time_num:.3f}s is before start time {start_time_num:.3f}s. clamping."
                )
                end_time_num = start_time_num
            start_str = format_timestamp(start_time_num)
            end_str = format_timestamp(end_time_num)
            srt_entries.append(f"{index}")
            srt_entries.append(f"{start_str} --> {end_str}")
            srt_entries.append(chunk["chunk_text"])
            srt_entries.append("")
            logger.debug(
                f"exported srt entry {index}: {start_str} --> {end_str}, text length: {len(chunk['chunk_text'])}"
            )
        except KeyError as e:
            logger.error(f"chunk {index} missing key {e}. skipping.")
        except ValueError as e:
            logger.error(f"error processing timestamps for chunk {index}: {e}. skipping.")
        except Exception as e:
            logger.exception(f"unexpected error exporting chunk {index}: {e}.")
    result = "\n".join(srt_entries)
    logger.info(f"completed export of srt, length: {len(result)} characters")
    return result


# --- forced alignment components ---


def levenshtein_distance(seq1: List[str], seq2: List[str]) -> int:
    """
    compute the levenshtein distance between two lists of strings.
    """
    m = len(seq1)
    n = len(seq2)
    dp = [[0] * (n + 1) for _ in range(m + 1)]
    for i in range(m + 1):
        dp[i][0] = i
    for j in range(n + 1):
        dp[0][j] = j
    for i in range(1, m + 1):
        for j in range(1, n + 1):
            cost = 0 if seq1[i - 1] == seq2[j - 1] else 1
            dp[i][j] = min(dp[i - 1][j] + 1, dp[i][j - 1] + 1, dp[i - 1][j - 1] + cost)
    return dp[m][n]


def forced_alignment(
    chunk_text: str, word_timestamps: List[Dict[str, Any]], current_start_idx: int
) -> Tuple[int, int, float]:
    """
    force align the chunk text with the word_timestamps using a sliding window and edit distance.
    this function does not discard any words. it searches over a window and selects the contiguous
    candidate segment (using normalized tokens) with minimal levenshtein distance to the transcript tokens.

    returns (start_index, end_index) within the word_timestamps list.
    """
    transcript_tokens = [
        normalize_word(word) for word in chunk_text.split() if normalize_word(word)
    ]
    if not transcript_tokens:
        raise ValueError("no valid tokens found in chunk_text.")

    candidate_tokens = [
        normalize_word(ts.get("word", "")) for ts in word_timestamps[current_start_idx:]
    ]
    if not candidate_tokens:
        raise ValueError("no candidate tokens available from word_timestamps.")

    # set search parameters.
    max_search_range = 100  # search within the next 100 tokens
    best_cost = float("inf")
    best_start = current_start_idx
    best_end = current_start_idx + len(transcript_tokens) - 1  # default guess

    max_candidate_length = min(len(candidate_tokens), max_search_range + len(transcript_tokens))

    # slide a window over candidate tokens.
    for offset in range(0, max_search_range):
        candidate_index = current_start_idx + offset
        # try candidate segment lengths within ±2 tokens of transcript length.
        for extra in range(-2, 3):
            seg_length = len(transcript_tokens) + extra
            if seg_length < 1:
                continue
            end_index = candidate_index + seg_length
            if end_index > current_start_idx + max_candidate_length:
                continue
            candidate_segment = candidate_tokens[
                candidate_index - current_start_idx : end_index - current_start_idx
            ]
            cost = levenshtein_distance(transcript_tokens, candidate_segment)
            if cost < best_cost:
                best_cost = cost
                best_start = candidate_index
                best_end = end_index - 1
                if best_cost == 0:  # perfect match
                    break
        if best_cost == 0:
            break

    logger.info(
        "forced alignment cost: {cost} for chunk; mapping candidate token indices {start} to {end}",
        cost=best_cost,
        start=best_start,
        end=best_end,
    )
    return best_start, best_end, best_cost


# --- timestampmapper class using forced alignment ---


class TimestampMapper:
    """
    map text chunks to timestamp ranges using forced alignment.

    this implementation uses a sliding-window search over the word-level timestamps,
    aligning each transcript chunk (tokenized and normalized) to a contiguous segment
    of word timestamps that minimizes the edit (levenshtein) distance. this way,
    every word—and every millisecond—is accounted for, and no chunks are discarded.
    """

    def __init__(self, similarity_threshold: float = 0.75) -> None:
        self.similarity_threshold = similarity_threshold
        logger.info("initialized timestampmapper with forced alignment approach.")

    def map_timestamps(
        self, word_timestamps: List[Dict[str, Any]], text_chunks: List[str]
    ) -> List[Dict[str, Any]]:
        if not word_timestamps:
            logger.warning("empty word_timestamps provided to map_timestamps")
            return []
        if not text_chunks:
            logger.warning("empty text_chunks provided to map_timestamps")
            return []
        logger.info(
            "starting forced alignment for {chunk_count} text chunks, {word_count} word timestamps",
            chunk_count=len(text_chunks),
            word_count=len(word_timestamps),
        )
        mapped_chunks = []
        current_start_idx = 0
        fallback_count = 0
        for chunk_idx, chunk_text in enumerate(text_chunks):
            logger.debug("processing chunk #{idx}: '{text}'", idx=chunk_idx, text=chunk_text)
            try:
                start_ts_idx, end_ts_idx, best_cost = forced_alignment(
                    chunk_text, word_timestamps, current_start_idx
                )
            except Exception as e:
                logger.exception(
                    "forced alignment failed for chunk #{idx}: {error}. using fallback.",
                    idx=chunk_idx,
                    error=e,
                )
                start_ts_idx = current_start_idx
                end_ts_idx = current_start_idx
                best_cost = float("inf")
            # fallback if no match found or indices out of bounds
            if (
                best_cost == float("inf")
                or start_ts_idx >= len(word_timestamps)
                or end_ts_idx >= len(word_timestamps)
            ):
                # estimate by assigning next len(tokens) words
                tokens = [normalize_word(w) for w in chunk_text.split() if normalize_word(w)]
                fallback_len = max(len(tokens), 1)
                start_ts_idx = current_start_idx
                end_ts_idx = min(current_start_idx + fallback_len - 1, len(word_timestamps) - 1)
                logger.warning(
                    "chunk #%d alignment failed (cost=%.1f) or out-of-range, falling back to indices %d–%d",
                    chunk_idx,
                    best_cost,
                    start_ts_idx,
                    end_ts_idx,
                )
                fallback_count += 1
            timestamp_values = self._extract_timestamp_values(
                chunk_idx, start_ts_idx, end_ts_idx, word_timestamps
            )
            if not timestamp_values:
                logger.error("failed to extract timestamp values for chunk #{idx}", idx=chunk_idx)
                continue
            start_secs, end_secs = timestamp_values
            mapped_chunk = {
                "chunk_text": chunk_text,
                "start": start_secs,
                "end": end_secs,
                "start_word_idx": start_ts_idx,
                "end_word_idx": end_ts_idx,
            }
            logger.info(
                "chunk #{idx} mapped: {start:.3f}s to {end:.3f}s (duration: {dur:.3f}s)",
                idx=chunk_idx,
                start=start_secs,
                end=end_secs,
                dur=end_secs - start_secs,
            )
            mapped_chunks.append(mapped_chunk)
            current_start_idx = end_ts_idx + 1
            if current_start_idx >= len(word_timestamps):
                logger.info("reached end of word timestamps at chunk #{idx}", idx=chunk_idx)
                break
        if fallback_count > 0:
            logger.warning("forced alignment fallback count: %d chunks", fallback_count)
        if len(mapped_chunks) != len(text_chunks):
            logger.warning(
                "forced alignment resulted in mapping {mapped} out of {total} chunks",
                mapped=len(mapped_chunks),
                total=len(text_chunks),
            )
        return mapped_chunks

    def _extract_timestamp_values(
        self,
        chunk_idx: int,
        start_ts_idx: int,
        end_ts_idx: int,
        word_timestamps: List[Dict[str, Any]],
    ) -> Optional[Tuple[float, float]]:
        if (
            start_ts_idx < 0
            or end_ts_idx < 0
            or start_ts_idx >= len(word_timestamps)
            or end_ts_idx >= len(word_timestamps)
        ):
            logger.error(
                "timestamp indices out of range for chunk #{idx}: start_ts_idx={start}, end_ts_idx={end} (max={max})",
                idx=chunk_idx,
                start=start_ts_idx,
                end=end_ts_idx,
                max=len(word_timestamps) - 1,
            )
            return None
        start_time_raw = word_timestamps[start_ts_idx].get("start")
        end_time_raw = word_timestamps[end_ts_idx].get("end")
        if not start_time_raw:
            logger.error(
                "missing 'start' field in word_timestamps[{idx}]: {entry}",
                idx=start_ts_idx,
                entry=word_timestamps[start_ts_idx],
            )
            return None
        if not end_time_raw:
            logger.error(
                "missing 'end' field in word_timestamps[{idx}]: {entry}",
                idx=end_ts_idx,
                entry=word_timestamps[end_ts_idx],
            )
            return None
        logger.debug(
            "for chunk #{idx}: selected word_timestamps[{start_idx}] to word_timestamps[{end_idx}]",
            idx=chunk_idx,
            start_idx=start_ts_idx,
            end_idx=end_ts_idx,
        )
        logger.debug(
            "raw timestamps - start: '{start}', end: '{end}'",
            start=start_time_raw,
            end=end_time_raw,
        )
        start_secs = self._convert_timestamp_to_seconds(start_time_raw, "start", chunk_idx)
        if start_secs is None:
            return None
        end_secs = self._convert_timestamp_to_seconds(end_time_raw, "end", chunk_idx)
        if end_secs is None:
            return None
        if end_secs < start_secs:
            logger.warning(
                "end time {end:.3f}s is earlier than start time {start:.3f}s - setting equal",
                end=end_secs,
                start=start_secs,
            )
            end_secs = start_secs
        return start_secs, end_secs

    def _convert_timestamp_to_seconds(
        self, timestamp_raw: Any, timestamp_type: str, chunk_idx: int
    ) -> Optional[float]:
        if isinstance(timestamp_raw, str):
            try:
                return parse_srt_timestamp(timestamp_raw)
            except Exception as e:
                logger.error(f"error parsing {timestamp_type} timestamp for chunk {chunk_idx}: {e}")
                return None
        elif isinstance(timestamp_raw, (int, float)):
            return float(timestamp_raw)
        else:
            logger.error(
                f"invalid type for '{timestamp_type}' timestamp in chunk {chunk_idx}: {type(timestamp_raw)}"
            )
            return None
