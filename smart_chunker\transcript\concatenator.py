from typing import Any, Dict, List

from loguru import logger


class JsonTextConcatenation:
    """
    a src that concatenates words from a json array provided as a parameter.
    """

    def __init__(self, data: List[Dict[str, Any]]) -> None:
        """
        initialize with json data.
        """
        self.data = data
        logger.info("initialized jsontextconcatenation with {count} items", count=len(data))

    def concatenate_words(self) -> str:
        """
        concatenate all 'word' fields into one single line.
        """
        words = [item["word"] for item in self.data if "word" in item]
        logger.debug("found {count} words to concatenate", count=len(words))
        concatenated = " ".join(words)
        logger.debug(
            "concatenated words result length: {length} characters",
            length=len(concatenated),
        )
        return concatenated
