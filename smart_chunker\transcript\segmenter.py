import ast
import json
import os
import re
from typing import List

from dotenv import load_dotenv
from google import genai
from google.genai import types
from loguru import logger
from pydantic import BaseModel, Field

load_dotenv()

JSON_BLOCK_REGEX = re.compile(r"```json\s*(.*?)\s*```", re.DOTALL)


def save_segmented_chunks_to_file(segmented_json: str, input_base: str, run_id: str) -> str:
    """
    saves the segmented chunks (json string) to a uniquely named file and logs the result.
    returns the output filename.
    """
    output_filename = f"{input_base}.segmented.{run_id}.json"
    try:
        # get file size before writing
        json_size_kb = len(segmented_json) / 1024
        chunk_count = 0

        # try to get chunk count if valid json
        try:
            chunk_data = json.loads(segmented_json)
            if isinstance(chunk_data, list):
                chunk_count = len(chunk_data)
        except json.JSONDecodeError:
            logger.warning("unable to parse json to count chunks")

        # write file
        with open(output_filename, "w", encoding="utf-8") as f:
            f.write(segmented_json)

        logger.info(
            "segmented chunks saved as: {filename} | size: {size:.2f} kb | chunks: {count}",
            filename=output_filename,
            size=json_size_kb,
            count=chunk_count,
        )
    except IOError as e:
        logger.error("failed to save segmented chunks file {}: {}", output_filename, e)
    return output_filename


def extract_json(llm_response: types.GenerateContentResponse) -> list:
    text = llm_response.text
    if not text:
        logger.error("empty response text received from language model.")
        return []
    match = JSON_BLOCK_REGEX.search(text)
    if not match:
        logger.error("no json block found in response. full response: {text}", text=text)
        return []
    json_text = match.group(1).strip()
    try:
        parsed_json = json.loads(json_text)
        logger.debug("successfully extracted json from response.")
        return parsed_json
    except json.JSONDecodeError as e:
        logger.exception("error parsing json: {error}", error=e)
        print(f"error parsing json: {e}")
        return []


def convert_to_valid_json(input_obj) -> str:
    """
    converts a python object (dict, list, etc.) or its string representation
    into a compact json string with double quotes and without additional whitespace.
    """
    if isinstance(input_obj, str):
        try:
            parsed_obj = ast.literal_eval(input_obj)
            logger.debug("converted input string to python object: {obj}", obj=parsed_obj)
        except Exception as e:
            logger.exception("provided string is not a valid python literal: {error}", error=e)
            raise ValueError("provided string is not a valid python literal.") from e
    else:
        parsed_obj = input_obj
    valid_json = json.dumps(parsed_obj, ensure_ascii=False, separators=(",", ":"))
    logger.debug(
        "converted object to valid json string of length: {length}",
        length=len(valid_json),
    )
    return valid_json


# Define the schema for chunked text segments
class TextChunk(BaseModel):
    """Schema for a text chunk with segmentation information."""
    text: str


class ChunkedResponse(BaseModel):
    """Schema for the full response containing all text chunks."""
    chunks: List[str] = Field(..., description="List of text segments that are optimized for translation")


class TranscriptionChunker:
    """
    a professional source to segment full transcribed text into coherent text chunks
    optimized for translation using google genai.

    the source clears conversation history except for the system prompt.
    """

    SYSTEM_PROMPT = (
        "**I. PERSONA & OBJECTIVE**\n"
        "You are an expert AI assistant specializing in text segmentation and enhancement of transcribed speech for optimal translation and speech synthesis (TTS) using ElevenLabs. Your objective is to process raw transcribed speech, which may have imperfect or missing punctuation and contain disfluencies (e.g., 'um,' 'uh'), and transform it into a series of well-structured, coherent text chunks. Each chunk must be optimized for independent translation and high-quality TTS output, incorporating ElevenLabs control decorators intelligently.\n\n"
        "**II. TASK: TEXT SEGMENTATION & ENHANCEMENT**\n"
        "Given a transcribed speech text provided in the `{{transcribed_text}}` placeholder, you must perform the following steps meticulously:\n\n"
        "1.  **Identify Natural Semantic Breaks:** Analyze the text to find logical points where one idea, topic, or sentence naturally concludes and another begins. Utilize cues such as:\n"
        "    *   Changes in subject matter.\n"
        "    *   Discourse markers (e.g., 'however,' 'therefore,' 'in addition,' 'so,' 'but').\n"
        "    *   Existing punctuation (if any, but treat with caution as it might be incorrect).\n"
        "    *   Your deep understanding of language structure to infer sentence boundaries when punctuation is absent or unreliable.\n\n"
        "2.  **Ensure Chunk Self-Containment & Coherence:**\n"
        "    *   Each chunk must be a coherent unit of meaning, ideally a complete thought or a set of closely related thoughts.\n"
        "    *   Strive for chunks that can be translated independently with minimal loss of contextual meaning. If a sentence heavily relies on the immediately preceding one for its core meaning, group them into the same chunk.\n"
        "    *   Group thematically linked sentences or those part of the same continuous thought process into a single chunk.\n"
        "    *   **Critical Constraint:** Absolutely avoid splitting a chunk mid-thought or mid-sentence. Ensure grammatical completeness where appropriate.\n\n"
        "3.  **Manage Chunk Length:** \n"
        "    *   Aim for chunks that are manageable for both translation APIs and TTS engines. Typically, this means chunks comparable in length to a standard written sentence or a very short paragraph (e.g., 1-3 related sentences).\n"
        "    *   Adjust length based on content to maintain semantic coherence above strict length rules.\n\n"
        "4.  **Preserve Original Text Integrity:**\n"
        "    *   **Critical Constraint:** Maintain the exact word order and content of the original transcribed text within each chunk. Do NOT rephrase, alter, summarize, or omit any words, including filler words (e.g., 'um,' 'uh') or perceived errors from the transcription. Each chunk must be a contiguous sequence of words from the input.\n\n"
        "5.  **Intelligently Insert Punctuation:**\n"
        "    *   Where punctuation is clearly missing or incorrect in the original transcription, you MAY insert appropriate standard English punctuation (periods, commas, question marks, exclamation marks, colons, semicolons, quotation marks) to improve readability, semantic clarity, and downstream processing quality.\n"
        "    *   Only add punctuation if you are highly confident of its grammatical correctness and placement. Prioritize clarity for translation and natural intonation for TTS.\n"
        "    *   Ensure questions end with a question mark to guide TTS intonation.\n\n"
        "6.  **Preserve Proper Nouns & Specialized Vocabulary:**\n"
        "    *   Detect and maintain all proper nouns (names of people, places, organizations), technical terms, brand names, titles, acronyms, and other specialized vocabulary in their original form without any modification or translation attempt.\n\n"
        "7.  **Add ElevenLabs Speech Control Decorators:**\n"
        "    *   Analyze each finalized chunk and strategically insert ElevenLabs control decorators to enhance TTS quality. Apply these sparingly and judiciously:\n"
        '        *   **Pauses (`<break />`):** Insert `<break time="X.Xs" />` tags at natural pause points within a chunk where a speaker would typically pause briefly. Use appropriate durations:\n'
        "            *   `0.3s` for very short, natural intra-sentence pauses (e.g., after a conjunction if the clause is long).\n"
        "            *   `0.7s` for medium pauses (e.g., between distinct but related ideas within a chunk, or at a significant comma).\n"
        "            *   `1.0s` to `1.5s` for longer, more deliberate pauses (e.g., for emphasis or transition between main points within a complex chunk). Maximum 3 seconds for very rare cases.\n"
        '            *   **Constraint:** Limit to a maximum of 1-2 `<break />` tags per chunk, only if truly beneficial for naturalness. Tags must be detached (e.g., `...end of phrase. <break time="0.5s" /> And then...`).\n'
        "        *   **Emphasis/Delivery Cues:**\n"
        "            *   For words or short phrases requiring specific emphasis, you MAY capitalize them (e.g., 'This is ABSOLUTELY vital.').\n"
        "            *   For segments with distinct emotional tone or delivery style needs (e.g., whispering, excitement, sarcasm), you MAY prepend a very brief, subtle narrative context cue in parentheses if it significantly aids the voice model (e.g., `(whispering) I think we should go.`, `(excitedly) We won!`). Use EXTREMELY sparingly, only when essential and natural.\n\n"
        "**III. FEW-SHOT EXAMPLES (Illustrative)**\n"
        "Input Text: `{{transcribed_text}}`\n\n"
        "**Example 1:**\n"
        "Input: \"hello there um how are you doing today it's a beautiful day isn't it\"\n"
        "Output (JSON format):\n"
        "```json\n"
        "{\n"
        '  "chunks": [\n'
        '    "Hello there. <break time=\\"0.3s\\" /> Um, how are you doing today?",\n'
        '    "It\'s a beautiful day, isn\'t it?"\n'
        "  ]\n"
        "}\n"
        "```\n\n"
        "**Example 2:**\n"
        'Input: "so i was wondering uh what time is the meeting and also will jane be there we need to confirm her attendance for the Project Alpha kickoff"\n'
        "Output (JSON format):\n"
        "```json\n"
        "{\n"
        '  "chunks": [\n'
        '    "So, I was wondering, uh, what time is the meeting? <break time=\\"0.7s\\" /> And also, will Jane be there?",\n'
        '    "We need to confirm her attendance for the Project Alpha kickoff."\n'
        "  ]\n"
        "}\n"
        "```\n\n"
        "**Example 3:**\n"
        'Input: "this is absolutely critical Mr Smith we need to finish this by five pm sharp or the entire deal falls through understand"\n'
        "Output (JSON format):\n"
        "```json\n"
        "{\n"
        '  "chunks": [\n'
        '    "This is ABSOLUTELY critical, Mr. Smith. <break time=\\"1.0s\\" /> We need to finish this by five PM sharp, or the entire deal falls through.",\n'
        '    "Understand?"\n'
        "  ]\n"
        "}\n"
        "```\n\n"
        "**IV. OUTPUT SPECIFICATION & RECAP**\n"
        "1.  **Format:** Provide your output STRICTLY as a JSON object with a 'chunks' field containing an array of strings. Each string in the array represents one processed and enhanced text chunk.\n"
        '    *   Example: `{"chunks": ["Chunk one with decorators.", "Chunk two with <break time=\\"0.5s\\" /> decorators."]}`\n'
        "2.  **Gemini API Recommendation:** If using the Gemini API, leverage the `responseSchema` feature to enforce this JSON structure for maximum reliability.\n"
        "3.  **Content:** Each chunk string must contain the original transcribed text (potentially with added punctuation) and any ElevenLabs decorators as per the rules in Section II.\n"
        "4.  **Critical Recap:** \n"
        "    *   Preserve ALL original words and their order.\n"
        "    *   Ensure chunks are semantically coherent and self-contained.\n"
        "    *   Apply ElevenLabs decorators sparingly and effectively.\n"
        "    *   Output valid JSON with a 'chunks' field containing the array of text chunks.\n\n"
        "### end of system instructions ###\n"
    )

    # ---
    # meta-documentation & guide recommendations applied to this prompt:
    # (this section is for human understanding and documents how the prompt aligns with best practices from "optimizing gemini prompts for production: a comprehensive guide to reliability, scalability, and efficiency")

    # 1.  **model-aware prompting strategy**:
    #     *   **suitability:** this prompt is designed for advanced reasoning and nuanced text manipulation, making it well-suited for gemini pro models. if targeting gemini flash, the "thinking budget" would be a key parameter to tune for balancing cost, latency, and quality.
    #     *   **no version specifics:** the prompt avoids hardcoding for a specific gemini version, aiming for general applicability with advanced models.

    # 2.  **clarity and structure (ptfc framework & foundational principles)**:
    #     *   **persona (p):** clearly defined as an "expert ai assistant specializing in text segmentation and enhancement of transcribed speech for optimal translation and speech synthesis (tts) using elevenlabs." (section i)
    #     *   **task (t):** extremely detailed step-by-step instructions for segmentation, punctuation, and decorator application. (section ii)
    #     *   **format (f):** explicitly defined as a "json object with a 'chunks' field containing an array of strings." (section iv) recommendation for `responseschema` with gemini api is included.
    #     *   **context (c):** placeholder `{{transcribed_text}}` for input, and comprehensive few-shot examples provide contextual learning. (section iii, v)
    #     *   **system instructions:** the entire prompt is framed as a comprehensive system instruction block, starting with "### system instructions...".
    #     *   **specificity & unambiguity:** uses precise language, defines constraints clearly (e.g., "absolutely avoid splitting...", "maintain the exact word order...").
    #     *   **conciseness vs. detail:** balances the need for detailed instructions for a complex task with clarity. redundancy is minimized while ensuring all critical aspects are covered.

    # 3.  **few-shot prompting (in-context learning)**:
    #     *   **inclusion:** three diverse few-shot examples are provided (section iii) to demonstrate:
    #         *   basic segmentation and punctuation.
    #         *   handling of filler words.
    #         *   application of `<break />` tags with varying durations.
    #         *   capitalization for emphasis.
    #         *   preservation of proper nouns.
    #         *   creation of question chunks.
    #     *   **quality over quantity:** examples are curated to be informative and cover different scenarios.
    #     *   **formatting consistency:** examples strictly follow the desired output json format.

    # 4.  **mastering structured output (json with `responseschema`)**:
    #     *   **mandated json:** the prompt mandates json output and provides the structure (list of strings).
    #     *   **`responseschema` recommendation:** explicitly advises using gemini's `responseschema` feature for enhanced reliability when using the api. (section iv)

    # 5.  **ensuring reliability and consistency**:
    #     *   **detailed instructions:** reduces ambiguity and guides the model towards consistent behavior.
    #     *   **few-shot examples:** significantly improve consistency by showing, not just telling.
    #     *   **constraints/guardrails:** explicit rules like "preserve original text integrity" and "avoid mid-thought splits" act as guardrails. (section ii)

    # 6.  **iterative prompt engineering lifecycle (implied)**:
    #     *   this prompt itself is a result of applying best practices, implying an iterative design process. for production, it would be version-controlled, tested against diverse inputs, and monitored.

    # 7.  **token economy**:
    #     *   while the prompt is detailed (increasing input tokens), this is a trade-off for higher accuracy and reliability on a complex task.
    #     *   few-shot examples add tokens but are crucial for quality.
    #     *   the output (chunked text) is managed by the task itself.

    # 8.  **security considerations (low relevance for this task but principles applied)**:
    #     *   **data minimization:** the prompt only asks for the transcribed text necessary for the task.
    #     *   **instructional defense (implicit):** by providing very specific instructions on what *to do*, it implicitly guides the model away from unintended actions. the task is not high-risk for injection.

    # 9.  **evaluation (implied framework for production)**:
    #     *   **kpis:** for this prompt, kpis would include:
    #         *   semantic coherence of chunks.
    #         *   translatability of chunks.
    #         *   naturalness of tts output (influenced by decorators and punctuation).
    #         *   adherence to formatting and decorator rules.
    #         *   preservation of original content.
    #     *   **methods:** a/b testing different decorator strategies, human evaluation of tts quality, and automated checks for json validity would be part of a production evaluation pipeline.

    # 10. **recap for critical requirements**:
    #      *   a dedicated "critical recap" subsection (section iv) reinforces the most important output constraints to minimize errors, especially for a complex set of instructions.

    # this structured approach, incorporating multiple best practices from the guide, aims to create a highly effective and reliable prompt for gemini in a production setting.
    # ---

    # system_prompt: str = (
    #     "you are an expert in text segmentation for translation and speech synthesis, specializing in transcribed speech. "
    #     'you are given a transcribed speech text, which may have imperfect or missing punctuation, and could contain errors or filler words (e.g., "um," "uh"). '
    #     "your task is to divide this text into well-structured chunks that are optimized for translation and speech synthesis, and enhance each chunk with elevenlabs control decorators to improve speech quality. "
    #     "each chunk should be a coherent unit of meaning, which could be a single sentence or a group of related sentences that together form a complete thought, "
    #     "suitable for translation independently with minimal loss of context.\n\n"
    #     "to achieve this, you should:\n"
    #     ' 1. identify natural breaks: look for points in the text where one idea or topic ends and another begins. use cues such as changes in subject, discourse markers (e.g., "however," "but," "in addition"), or any available punctuation. if punctuation is absent, rely on your understanding of the language to infer where sentences or thoughts conclude.\n'
    #     " 2. ensure self-containment: make each chunk as self-contained as possible, providing enough context to be understood independently during translation. if a sentence heavily depends on a previous one for meaning, include it in the same chunk.\n"
    #     " 3. group related content: combine sentences that are thematically linked or part of the same thought process into a single chunk.\n"
    #     " 4. avoid mid-thought splits: do not split a chunk in the middle of a thought or idea; keep sentences together if they contribute to a larger concept.\n"
    #     " 5. manage chunk length: keep chunks manageable for translation, typically not exceeding the length of a standard sentence or a short paragraph. aim for chunks similar in length to a sentence or short paragraph in written text, but adjust based on content to maintain coherence.\n"
    #     " 6. preserve original text: maintain the exact word order and content of the original text; do not rephrase, alter, or omit any words, including filler words or errors. each chunk must be a contiguous sequence of words from the input.\n"
    #     " 7. intelligently insert punctuation: you may add appropriate punctuation symbols (commas, periods, quotation marks, colons, semicolons) where they are clearly missing to improve readability and translation quality, but only when you are confident about their placement.\n"
    #     " 8. preserve proper nouns: detect and maintain all proper nouns, technical terms, brand names, titles, and other specialized vocabulary in their original form without translation.\n\n"
    #     " 9. add elevenlabs speech control decorators: analyze each chunk and add appropriate elevenlabs control decorators to enhance the speech synthesis quality. specifically:\n"
    #     "     a. add pause tags ` <break time=\"x.xs\" /> ` at natural pause points where a speaker would naturally pause, using appropriate durations (0.3s for short pauses, 0.7s for medium pauses, 1.2s for longer pauses, up-to 3 seconds). do not overuse these tags - limit to 1-2 per chunk at most. pause point tags must be detached from the text, not attached to any word or phrase, like this: `this is a <break time=\"0.3s\" /> pause`\n"
    #     "     b. for words or phrases with emotional emphasis or special delivery needs, consider adding subtle narrative context that guides the voice model (e.g., \"he whispered,\" \"she exclaimed angrily,\") but use this sparingly.\n"
    #     "     c. for segments containing questions, ensure they end with appropriate question mark punctuation to guide intonation.\n"
    #     "     d. for words or phrases that need specific emphasis, you may capitalize them or add subtle delivery cues.\n\n"
    #     "output format: provide your output as a json list of chunks, where each chunk is a string of text that includes the original content enhanced with appropriate elevenlabs control decorators."
    # )

    def __init__(
        self,
        api_key: str = None,
        model: str = "gemini-2.5-pro-preview-05-06",
        temperature: int = "0.2",
    ) -> None:
        """
        initialize the TranscriptionChunker with api credentials and model configuration.

        :param api_key: api key for authentication. if none, read from the google_api_key environment variable.
        :param model: the model used for content generation.
        :raises valueerror: if no api key is provided.
        """
        self.api_key = api_key or os.environ.get("GOOGLE_API_KEY")
        if not self.api_key:
            logger.critical("no api key provided for transcriptionchunker.")
            raise ValueError(
                "api key must be provided via parameter or google_api_key environment variable."
            )
        self.client = genai.Client(api_key=self.api_key)
        self.model = model
        self.temperature = temperature
        self.response = None
        logger.info(
            "initialized transcriptionchunker with model: {model} and temperature: {temp}",
            model=model,
            temp=temperature,
        )

    def segment_text(self, transcribed_text: str) -> str:
        """
        segment the full transcribed text into coherent text chunks optimized for translation.

        the conversation history is cleared, preserving only the system prompt and current user input.

        :param transcribed_text: the complete transcribed text to be segmented.
        :return: a compact json-formatted string containing the list of text chunks.
        """
        logger.info(
            "starting text segmentation. input text length: {length} characters",
            length=len(transcribed_text),
        )

        # log input data sample (first 200 chars)
        input_sample = (
            transcribed_text[:200] + "..." if len(transcribed_text) > 200 else transcribed_text
        )
        logger.debug("input text sample: {sample}", sample=input_sample)

        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part.from_text(
                        text=(
                            "**V. INPUT TEXT**\n"
                            "Process the following transcribed text:\n"
                            f"`{transcribed_text}`\n\n"
                        )
                    )
                ],
            ),
        ]
        logger.debug("prepared content for segmentation.")

        # define the response schema using a Pydantic model
        generate_content_config = types.GenerateContentConfig(
            temperature=self.temperature,
            response_mime_type="application/json",
            system_instruction=[types.Part.from_text(text=self.SYSTEM_PROMPT)],
            max_output_tokens=65536,
            response_schema=ChunkedResponse
        )
        logger.debug("generate content configuration created for segmentation with schema.")

        output = self.client.models.generate_content(
            model=self.model,
            contents=contents,
            config=generate_content_config,
        )
        logger.debug("received response from model during segmentation.")

        # with schema-based generation, the response is directly parsed
        chunks_list = output.parsed.chunks if hasattr(output, 'parsed') and output.parsed is not None else []

        # handle case where parsed might not be available
        if not chunks_list and output.text:
            # fallback to text parsing if needed
            try:
                chunks_list = json.loads(output.text)
                logger.debug("parsed chunks from response text.")
            except json.JSONDecodeError:
                # last resort: try to extract using regex
                extracted = extract_json(output)
                if extracted:
                    chunks_list = extracted
                    logger.debug("extracted chunks using regex fallback.")
                else:
                    logger.error("failed to parse response into chunks.")
                    chunks_list = []  # Ensure we have an empty list, not None

        valid_json = convert_to_valid_json(chunks_list)

        # log output data sample (first 3 chunks, up to 200 chars each)
        if chunks_list:
            sample_chunks = []
            for i, chunk in enumerate(chunks_list[:3]):
                if isinstance(chunk, str):
                    sample_text = chunk[:200] + "..." if len(chunk) > 200 else chunk
                    sample_chunks.append(f"chunk {i+1}: {sample_text}")
            logger.debug("output chunks sample: {sample}", sample="\n".join(sample_chunks))

        logger.info(
            "text segmentation complete. generated {count} chunks. valid json length: {length} characters",
            count=len(chunks_list),
            length=len(valid_json),
        )
        return valid_json
