"""
batch processing for translations
"""

import concurrent.futures
import os
import threading
import time
from typing import Any, Callable, Dict, List, Tuple

from loguru import logger

from smart_chunker.utils.retry import retry_with_backoff

# conditional import to avoid circular dependencies
try:
    from smart_chunker.translation.translator import DubSegmentTranslation

    DUB_SEGMENT_AVAILABLE = True
except ImportError:
    DUB_SEGMENT_AVAILABLE = False


class ParallelTranslationProcessor:
    """
    unified parallel processor for translation requests.

    supports two modes:
    1. generic mode: provide your own translator_func (from process_batch)
    2. integrated mode: uses DubSegmentTranslation directly (from translate_batch)

    both modes support parallelism and rate limiting.
    """

    def __init__(
        self,
        max_workers: int = 5,
        rate_limit_rpm: int = 150,
        api_key: str = None,
        model: str = "gemini-2.5-pro-preview-05-06",
        temperature: float = 0.2,
    ):
        """
        initialize the processor with parallelism and rate limiting parameters

        args:
            max_workers: maximum number of parallel workers
            rate_limit_rpm: rate limit in requests per minute
            api_key: optional api key for integrated mode (from env if not provided)
            model: model name for integrated mode
            temperature: temperature setting for integrated mode
        """
        self.max_workers = max_workers
        self.rate_limit_rpm = rate_limit_rpm
        self.request_delay = (60.0 / rate_limit_rpm) * 0.9  # add small buffer

        # rate limiting variables
        self._lock = threading.Lock()
        self._last_request_time = 0.0

        # integrated mode variables (only initialized if needed)
        self.api_key = api_key
        self.model = model
        self.temperature = temperature
        self._translator = None

        logger.info(
            "initialized parallel translation processor with {workers} workers at {rpm} rpm",
            workers=max_workers,
            rpm=rate_limit_rpm,
        )

    def _get_translator(self):
        """lazy initialization of translator instance"""
        if not self._translator and DUB_SEGMENT_AVAILABLE:
            self.api_key = self.api_key or os.environ.get("GOOGLE_API_KEY")
            if not self.api_key:
                raise ValueError(
                    "api key must be provided via parameter or 'GOOGLE_API_KEY' environment variable."
                )

            self._translator = DubSegmentTranslation(
                api_key=self.api_key, model=self.model, temperature=self.temperature
            )

            logger.debug(
                "created dubsegmenttranslation instance with model: {model}", model=self.model
            )

        return self._translator

    def _apply_rate_limiting(self):
        """apply rate limiting logic, should be called before each request"""
        with self._lock:
            current_time = time.time()
            time_since_last = current_time - self._last_request_time

            if time_since_last < self.request_delay:
                sleep_time = self.request_delay - time_since_last
                logger.debug("rate limiting: sleeping for {time:.3f}s", time=sleep_time)
                time.sleep(sleep_time)

            self._last_request_time = time.time()

    @retry_with_backoff(max_retries=3)
    def _translate_segment_generic(
        self, segment: Dict, translator_func: Callable, **kwargs
    ) -> Dict:
        """translate a single segment with retry logic and rate limiting (generic mode)"""
        # apply rate limiting
        self._apply_rate_limiting()

        # call the provided translator function
        return translator_func(segment, **kwargs)

    def _translate_segment_integrated(
        self, segment_id: str, text_request: Dict[str, Any], style_request: Dict[str, Any]
    ) -> Tuple[str, Dict[str, Any]]:
        """
        translate a single segment with rate limiting using integrated translator.
        returns a tuple of (segment_id, translation_data)
        """
        # apply rate limiting
        self._apply_rate_limiting()

        # translate segment
        try:
            translator = self._get_translator()
            translation_data = translator.translate_segment(text_request, style_request)
            return segment_id, translation_data
        except Exception as e:
            logger.error("failed to translate segment {id}: {error}", id=segment_id, error=str(e))
            return segment_id, {"translated_segment": "", "translation_notes": {"error": str(e)}}

    def process_batch(
        self, segments: List[Dict], translator_func: Callable, **kwargs
    ) -> List[Dict]:
        """
        process a batch of segments in parallel (generic mode)

        args:
            segments: list of segment dictionaries to translate
            translator_func: function to translate a single segment
            **kwargs: additional arguments to pass to translator_func

        returns:
            list of translated segments
        """
        logger.info("processing batch of {count} segments in parallel", count=len(segments))

        translated_segments = []
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_segment = {
                executor.submit(
                    self._translate_segment_generic, segment, translator_func, **kwargs
                ): segment
                for segment in segments
            }

            for future in concurrent.futures.as_completed(future_to_segment):
                try:
                    translated_segment = future.result()
                    translated_segments.append(translated_segment)
                except Exception as e:
                    segment = future_to_segment[future]
                    logger.error("error translating segment: {}", str(e))
                    # add the original segment to maintain order
                    translated_segments.append(segment)

        # sort segments by original index if they have an index field
        if translated_segments and "index" in translated_segments[0]:
            translated_segments.sort(key=lambda x: x.get("index", 0))

        return translated_segments

    def translate_segments(
        self, segments: Dict[str, Tuple[Dict[str, Any], Dict[str, Any]]]
    ) -> Dict[str, Dict[str, Any]]:
        """
        translate multiple segments in parallel (integrated mode)

        args:
            segments: dictionary mapping segment_ids to tuples of (text_request, style_request)

        returns:
            dictionary mapping segment_ids to their translation data
        """
        results = {}
        total_segments = len(segments)

        logger.info(
            "starting parallel translation of {count} segments with {workers} workers",
            count=total_segments,
            workers=min(self.max_workers, total_segments),
        )

        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # submit all tasks
            future_to_segment = {
                executor.submit(
                    self._translate_segment_integrated, segment_id, text_request, style_request
                ): segment_id
                for segment_id, (text_request, style_request) in segments.items()
            }

            # collect results as they complete
            for i, future in enumerate(concurrent.futures.as_completed(future_to_segment), 1):
                segment_id = future_to_segment[future]
                try:
                    segment_id, translation_data = future.result()
                    results[segment_id] = translation_data

                    # extract a preview of the translation to show in the log
                    translated_segment = translation_data.get("translated_segment", "")
                    preview = (
                        (translated_segment[:50] + "...")
                        if len(translated_segment) > 50
                        else translated_segment
                    )

                    logger.info(
                        "completed {i}/{total} - segment: {id} translation: {preview}",
                        i=i,
                        total=total_segments,
                        id=segment_id,
                        preview=preview,
                    )
                except Exception as e:
                    logger.error(
                        "exception translating segment {id}: {error_type} - {error}",
                        id=segment_id,
                        error_type=type(e).__name__,
                        error=str(e),
                    )
                    results[segment_id] = {
                        "translated_segment": "",
                        "translation_notes": {"error": str(e)},
                    }

        logger.info("completed parallel translation of {count} segments", count=total_segments)
        return results

    def translate_batch(
        self,
        segments_to_translate: List[str],
        full_text: str,
        current_language: str,
        target_language: str,
        style_request: Dict[str, Any],
    ) -> Dict[str, Dict[str, Any]]:
        """
        convenient method to translate a batch of segments with common context and style (integrated mode)

        args:
            segments_to_translate: list of text segments to translate
            full_text: the full text for context
            current_language: source language code
            target_language: target language code
            style_request: style parameters for translation

        returns:
            dictionary mapping segment indices to their translation data
        """
        segments = {}

        for i, segment in enumerate(segments_to_translate):
            segment_id = str(i)
            text_request = {
                "current_language": current_language,
                "target_language": target_language,
                "full_text": full_text,
                "segment_to_translate": segment,
            }
            segments[segment_id] = (text_request, style_request)

        return self.translate_segments(segments)
