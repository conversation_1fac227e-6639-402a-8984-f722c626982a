import ast
import json
import os
import re
from typing import Any, Dict

from dotenv import load_dotenv
from google import genai
from google.genai import types
from loguru import logger

from smart_chunker.utils.retry import retry_with_backoff

load_dotenv()

JSON_BLOCK_REGEX = re.compile(r"```json\s*(.*?)\s*```", re.DOTALL)


def extract_json(llm_response: types.GenerateContentResponse) -> dict:
    text = llm_response.text
    if not text:
        logger.error("empty response text received from language model.")
        return {}
    match = JSON_BLOCK_REGEX.search(text)
    if not match:
        # if no json block with markdown code formatting, try to parse the entire response as json
        try:
            parsed = json.loads(text)
            logger.debug(
                "parsed entire response as json with length {length}",
                length=len(text),
            )
            return parsed
        except json.JSONDecodeError:
            logger.error(
                "no json block found in response and couldn't parse as json. response text: {text}",
                text=text,
            )
            return {}

    json_text = match.group(1).strip()
    try:
        parsed = json.loads(json_text)
        logger.debug(
            "successfully extracted json from response with length {length}",
            length=len(json_text),
        )
        return parsed
    except json.JSONDecodeError as e:
        logger.exception("error parsing json: {error}", error=e)
        return {}


def convert_to_valid_json(input_obj: Any) -> str:
    """
    converts a python dictionary or a string representation of a dictionary
    into a valid json string with double quotes and without extra indentation.

    parameters:
        input_obj (dict or str): either a python dict or a string representation of a dict.

    returns:
        str: a compact json-formatted string.
    """
    if isinstance(input_obj, str):
        try:
            parsed_obj = ast.literal_eval(input_obj)
            logger.debug(
                "input string successfully converted to python object: {obj}",
                obj=parsed_obj,
            )
        except Exception as e:
            logger.exception("provided string is not a valid python literal: {error}", error=e)
            raise ValueError("provided string is not a valid python literal.") from e
    elif isinstance(input_obj, dict):
        parsed_obj = input_obj
        logger.debug("input is a dictionary; proceeding with json conversion.")
    else:
        logger.error("invalid input type received: {type}", type=type(input_obj))
        raise TypeError("input must be either a dictionary or a string representing a dictionary.")
    valid_json = json.dumps(parsed_obj, ensure_ascii=False, separators=(",", ":"))
    logger.debug(
        "converted object to valid json string, length: {length}",
        length=len(valid_json),
    )
    return valid_json


class TranslationRequestAnalyzer:
    """
    senior-level analyzer for translation requests.
    analyzes input json and produces structured translation parameters.
    """

    def __init__(
        self,
        api_key: str = None,
        model: str = "gemini-2.5-pro-preview-05-06",
        temperature: float = 0.2,
    ) -> None:
        self.api_key = api_key or os.environ.get("GOOGLE_API_KEY")
        if not self.api_key:
            logger.critical("api key is required for translationrequestanalyzer.")
            raise ValueError("api key is required.")
        self.client = genai.Client(api_key=self.api_key)
        self.model = model
        self.temperature = temperature
        logger.info(
            "initialized translationrequestanalyzer with model: {model} and temperature: {temp}",
            model=self.model,
            temp=self.temperature,
        )

    def analyze_request(self, request: Dict[str, Any]) -> str:
        """
        analyze a translation request and return a structured json analysis.

        expected input format:
        {
          "source_language": "uz",
          "target_language": "en",
          "full_text": "..."
        }

        output json structure as a string with double quotes:
        {
          "style_markers": { ... },
          "tone": "...",
          "register": "...",
          "target_audience": "...",
          "cultural_context": "...",
          "specialized_terminology": { ... },
          "previous_translations": { ... }
        }
        """
        logger.info("starting analysis of translation request.")
        input_text = json.dumps(request, ensure_ascii=False)
        logger.debug(
            "prepared input text for analysis (length: {length})",
            length=len(input_text),
        )
        content = types.Content(role="user", parts=[types.Part.from_text(text=input_text)])

        system_instruction = (
            "you are a translation request analyzer that prepares detailed input data for a literary translation system. "
            "your role is to analyze text and create structured json requests that include all contextual elements necessary for high-quality translation."
            "# input format"
            "you will receive text content:"
            '```json {"source_language":"english","target_language":"spanish","full_text":"the complete original text that provides context..."}```'
            "# output format"
            "you must produce a structured json object with double quotes for all keys and string values. "
            "single quotes are not allowed. format the response as a valid json object without code blocks or additional text."
            "the json must follow this structure:"
            '{"style_markers":{"formality":"[formal/semi-formal/informal/colloquial]","sentence_structure":"[simple/moderate/complex]"},'
            '"tone":"[emotional quality: formal/informal/academic/conversational/etc]","register":"[language register: colloquial/standard/formal/technical/poetic/etc]",'
            '"target_audience":"[intended readers: general/technical/academic/children/etc]","cultural_context":"[relevant cultural background]",'
            '"specialized_terminology":{"term1":"preferred_translation1","term2":"preferred_translation2"},'
            '"previous_translations":{"recurring_phrase1":"established_translation1","character_name1":"established_translation2"}}'
            "# parameter definition rules"
            "1. **style_markers**:"
            "    - analyze sentence length, complexity, vocabulary choices"
            "    - identify literary devices (metaphor, simile, alliteration, etc.)"
            "    - determine formality level from contextual cues"
            "2. **tone**:"
            "    - select one primary tone: formal, informal, serious, humorous, melancholic, optimistic, etc."
            "      - base on author's word choice, punctuation, and thematic elements"
            "3. **register**:"
            "    - identify language register from: colloquial, standard, formal, technical, academic, poetic"
            "    - consider vocabulary sophistication and specialized terminology density"
            "4. **target_audience**:"
            "    - infer from content complexity, terminology, and cultural references"
            "    - common categories: general public, specialists, academics, children, young adults"
            "5. **cultural_context**:"
            "    - identify cultural references, historical period, geographic setting"
            "    - note culture-specific concepts that may require adaptation"
            "6. **specialized_terminology**:"
            "    - extract domain-specific terms from the text"
            "    - for recurring technical or specialized terms, provide preferred translations if identifiable"
            "7. **previous_translations**:"
            "    - identify recurring phrases, names, or terms in the full text"
            "    - for subsequent segments, maintain consistency with how these were previously translated"
            "when analyzing non-english source texts, determine parameters based on universal linguistic features rather than language-specific elements."
            "important: your output should only contain the json object with double quotes for all keys and string values. "
            "do not include any explanation text, markdown formatting, or code blocks. return only the raw json object."
        )
        logger.debug(
            "system instruction for analysis prepared (length: {length} characters)",
            length=len(system_instruction),
        )

        generate_config = types.GenerateContentConfig(
            temperature=self.temperature,
            response_mime_type="application/json",
            system_instruction=[types.Part.from_text(text=system_instruction)],
        )
        logger.debug(
            "generatecontentconfig created with temperature {temp}",
            temp=self.temperature,
        )

        output = self.client.models.generate_content(
            model=self.model, contents=[content], config=generate_config
        )
        logger.info("received response from language model for translation request analysis.")

        analysis_result = extract_json(output)

        # ensure the result is a valid json string with double quotes
        if not analysis_result:
            logger.warning("empty analysis result, returning empty json.")
            return "{}"

        # convert the python dictionary to a valid json string with double quotes
        json_result = json.dumps(analysis_result, ensure_ascii=False)
        return json_result


class DubSegmentTranslation:
    """
    expert literary translator agent for dubbing segments.
    translates segments from {current_language} to {target_language} using the full_text as context.
    style instructions are provided separately.
    """

    def __init__(
        self,
        api_key: str = None,
        model: str = "gemini-2.5-pro-preview-05-06",
        temperature: float = 0.2,
    ) -> None:
        self.api_key = api_key or os.environ.get("GOOGLE_API_KEY")
        if not self.api_key:
            logger.critical("api key is required for dubsegmenttranslation.")
            raise ValueError("api key is required.")
        self.client = genai.Client(api_key=self.api_key)
        self.model = model
        self.temperature = temperature
        logger.info(
            "initialized dubsegmenttranslation with model: {model} and temperature: {temp}",
            model=model,
            temp=temperature,
        )

    @retry_with_backoff()
    def translate_segment(
        self, text_request: Dict[str, Any], style_request: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        translate a dub segment based on two separate json objects.

        the first json object (text_request) contains text-related parameters.
        the second json object (style_request) contains style-related parameters.
        """
        logger.info("starting translation of segment.")
        # prepare text and style messages as separate chat parts using compact json formatting.
        try:
            text_json = json.dumps(text_request, ensure_ascii=False, separators=(",", ":"))
            # logger.debug("text request json: {json}", json=text_json)
            text_message = types.Part.from_text(text=text_json)
        except Exception as e:
            logger.exception("error converting text_request to json: {error}", error=e)
            raise

        try:
            style_json = json.dumps(style_request, ensure_ascii=False, separators=(",", ":"))
            # logger.debug("style request json: {json}", json=style_json)
            style_message = types.Part.from_text(text=style_json)
        except Exception as e:
            logger.exception("error converting style_request to json: {error}", error=e)
            raise

        # updated system prompt with separate input instructions
        system_instruction = (
            "you are an expert literary translator. your task is to translate text segments from the current_language to the target_language, "
            "using the full_text as context and following the style instructions provided in a separate message.\n\n"
            "your translation should prioritize the following:\n\n"
            "*   **accuracy:** semantic and contextual equivalence. precise rendering of specialized terminology.\n"
            "*   **artistry:** recreate rhythm, cadence, and flow. adapt sentence structures creatively for readability.\n"
            "*   **style:** emulate the author's stylistic devices (e.g., stream of consciousness, alliteration, irony).\n"
            "*   **cultural nuance:** localize untranslatable elements with analogous expressions. replace culture-specific proverbs with equivalents.\n"
            "*   **refinement:** ensure elegance and naturalness. avoid stiff or overly academic phrasing.\n"
            "*   **alignment:** no thematic or narrative deviations. metaphors and imagery should evoke the same response.\n"
            "*   **quality assurance:** eliminate omissions, inaccuracies, or tonal inconsistencies.\n\n"
            "**important notes:**\n"
            "*   for dialogue, prioritize natural speech patterns in the target language.\n"
            "*   preserve deliberate stylistic choices (repetition, fragmented syntax) unless they hinder comprehension.\n"
            "#### input format\n"
            "you will receive two json objects in separate messages:\n\n"
            "1. text object example:\n"
            "```json\n"
            "{\n"
            '  "current_language": "uz",\n'
            '  "target_language": "en",\n'
            '  "full_text": "assalomu alaykum, hurmatli tinglovchilar. ...",\n'
            '  "segment_to_translate": "birinchisi, fishing va ijtimoiy injeneriya tushunchasi, zararkunanda kod, offlayn tahlil tushunchalari va ikkinchisi parollarni buzish vositalari, kriptografik hech funksiyalar."\n'
            "}\n"
            "```\n\n"
            "2. style object example:\n"
            "```json\n"
            "{\n"
            '  "style_markers": {"formality": "formal", "sentence_structure": "moderate"},\n'
            '  "tone": "academic",\n'
            '  "register": "technical",\n'
            '  "target_audience": "technical",\n'
            '  "cultural_context": "educational setting (likely a lecture or presentation on cybersecurity). the greeting \'assalomu alaykum\' is a formal uzbek greeting.",\n'
            '  "specialized_terminology": {"xavfsizlik asoslari fani": "basics of security course/subject", "zararkunanda dasturiy ta\'minot": "malicious software", "fishing": "phishing", "ijtimoiy injeneriya": "social engineering", "zararkunanda kod": "malicious code", "offlayn tahlil": "offline analysis", "parollarni buzish vositalari": "password cracking tools", "kriptografik hech funksiyalar": "cryptographic hash functions", "kiber jinoyat": "cybercrime"},\n'
            '  "previous_translations": {}\n'
            "}\n"
            "```\n\n"
            "#### output format\n"
            "return only the translated segment using the following json format:\n\n"
            "```json\n"
            "{\n"
            '  "translated_segment": "translated text segment",\n'
            '  "translation_notes": {\n'
            '    "challenging_elements": "challenges encountered during translation",\n'
            '    "style_adaptations": "how style elements were preserved"\n'
            "  }\n"
            "}\n"
            "```\n\n"
            "follow the translation principles strictly, ensuring accuracy, stylistic fidelity, tonal consistency, register appropriateness, audience awareness, cultural sensitivity, terminology precision, and translation consistency. "
            "reference previous translations where applicable."
        )
        logger.debug(
            "system instruction for translation prepared (length: {length} characters)",
            length=len(system_instruction),
        )

        # create content list with two messages: first text, then style.
        text_content = types.Content(role="user", parts=[text_message])
        style_content = types.Content(role="user", parts=[style_message])
        logger.debug("created content messages for text and style.")

        generate_config = types.GenerateContentConfig(
            max_output_tokens=65536,
            temperature=self.temperature,
            response_mime_type="text/plain",
            system_instruction=[types.Part.from_text(text=system_instruction)],
        )
        logger.debug(
            "generatecontentconfig created with temperature {temp}",
            temp=self.temperature,
        )

        response = self.client.models.generate_content(
            model=self.model,
            contents=[text_content, style_content],
            config=generate_config,
        )
        logger.info("received response from language model for translation.")

        result_json = extract_json(response)
        logger.debug("extracted translation json: {result}", result=result_json)
        return result_json


if __name__ == "__main__":
    translator = DubSegmentTranslation()
    text_data = {
        "current_language": "uz",
        "target_language": "en",
        "full_text": (
            "assalomu alaykum, hurmatli tinglovchilar. bugungi bir xavfsizlik asoslari fanimizning navbatdagi mavzusi juda dolzarb "
            "bugungi kundagi mavzulardan biri hisoblanadi. ya'ni zararkunanda dasturiy ta'minot haqida qisqacha gaplashamiz. "
            "demak, mavzumizda quyidagi rejada keltirilgan savollar bilan tanishib chiqamiz. birinchisi, fishing va ijtimoiy injeneriya "
            "tushunchasi, zararkunanda kod, offlayn tahlil tushunchalari va ikkinchisi parollarni buzish vositalari, kriptografik hech funksiyalar. "
            "dastlab fishing tushunchasi haqida qisqacha ma'lumotga ega bo'lishimiz kerak. ya'ni bu yerda prezentatsiyada ham keltirilgan. "
            "fishing bu kiber jinoyat"
        ),
        "segment_to_translate": (
            "dastlab fishing tushunchasi haqida qisqacha ma'lumotga ega bo'lishimiz kerak. ya'ni bu yerda prezentatsiyada ham keltirilgan."
        ),
    }
    style_data = {
        "style_markers": {"formality": "formal", "sentence_structure": "moderate"},
        "tone": "academic",
        "register": "technical",
        "target_audience": "technical",
        "cultural_context": "educational setting (likely a lecture or presentation on cybersecurity). the greeting 'assalomu alaykum' is a formal uzbek greeting.",
        "specialized_terminology": {
            "xavfsizlik asoslari fani": "basics of security course/subject",
            "zararkunanda dasturiy ta'minot": "malicious software",
            "fishing": "phishing",
            "ijtimoiy injeneriya": "social engineering",
            "zararkunanda kod": "malicious code",
            "offlayn tahlil": "offline analysis",
            "parollarni buzish vositalari": "password cracking tools",
            "kriptografik hech funksiyalar": "cryptographic hash functions",
            "kiber jinoyat": "cybercrime",
        },
        "previous_translations": {},
    }

    # test individual translation
    result = translator.translate_segment(text_data, style_data)
    logger.info("individual translation result: {result}", result=result)

    # test parallel translation
    from smart_chunker.translation import ParallelTranslationProcessor

    parallel_translator = ParallelTranslationProcessor()

    # create a few test segments
    segments_to_translate = [
        "dastlab fishing tushunchasi haqida qisqacha ma'lumotga ega bo'lishimiz kerak.",
        "ya'ni bu yerda prezentatsiyada ham keltirilgan.",
        "fishing bu kiber jinoyat bo'lib, bu insonlarni aldash uchun ishlatiladi.",
    ]

    full_text = text_data["full_text"]

    # test batch translation
    batch_results = parallel_translator.translate_batch(
        segments_to_translate=segments_to_translate,
        full_text=full_text,
        current_language="uz",
        target_language="en",
        style_request=style_data,
    )

    logger.info("batch translation results: {count} segments processed", count=len(batch_results))
    for segment_id, translation in batch_results.items():
        logger.info(
            "segment {id}: {translation}",
            id=segment_id,
            translation=translation["translated_segment"],
        )

    print(convert_to_valid_json(result))
