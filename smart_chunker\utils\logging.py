"""
logging utility for smart chunker.
provides consistent logging format and message style enforcement.
"""

import functools
import sys
from typing import Any, Callable, TypeVar, cast

from loguru import logger

# ensure colorama is initialized for windows color support
try:
    import colorama

    colorama.init()
except ImportError:
    pass  # colorama is optional, but recommended for windows

# function return type for decorator typing
F = TypeVar("F", bound=Callable[..., Any])


def lowercase_messages(func: F) -> F:
    """decorator to ensure log messages are always lowercase.

    args:
        func: logging function to wrap

    returns:
        wrapped function that converts message to lowercase
    """

    @functools.wraps(func)
    def wrapper(message, *args, **kwargs):
        # check if it's a string (could be an exception or other object)
        if isinstance(message, str):
            message = message.lower()
        return func(message, *args, **kwargs)

    return cast(F, wrapper)


def apply_lowercase_to_logger():
    """apply lowercase decorator to all logger methods"""
    for level in ["trace", "debug", "info", "success", "warning", "error", "critical"]:
        if hasattr(logger, level):
            setattr(logger, level, lowercase_messages(getattr(logger, level)))


def setup_logger(level: str = "DEBUG", apply_lowercase: bool = True):
    """configure and return the logger with advanced formatting and colorization

    args:
        level: logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        apply_lowercase: whether to apply lowercase decorator to all log messages

    returns:
        configured logger instance
    """
    logger.remove()
    logger.add(
        sys.stdout,
        format=(
            "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        ),
        colorize=True,
        backtrace=True,
        diagnose=True,
        level=level,
    )

    # file logging disabled - only console logging enabled

    if apply_lowercase:
        apply_lowercase_to_logger()

    return logger


# create a global logger instance ready to use
default_logger = setup_logger()
