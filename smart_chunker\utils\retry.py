"""
retry utilities for handling transient errors in api calls.
"""

import time
from functools import wraps
from typing import Any, Callable, List, Optional, Type, Union

from loguru import logger

DEFAULT_EXCEPTIONS = (Exception,)
DEFAULT_MAX_RETRIES = 3
DEFAULT_INITIAL_DELAY = 1.0  # seconds
DEFAULT_MAX_DELAY = 30.0  # seconds
DEFAULT_BACKOFF_FACTOR = 2.0


def _handle_retry(
    retry_count: int,
    delay: float,
    max_retries: int,
    e: Exception,
    on_retry: Optional[Callable] = None,
) -> bool:
    """
    handle retry logic, logging, and callbacks.

    args:
        retry_count: current retry attempt count
        delay: current delay before next retry
        max_retries: maximum number of retries allowed
        e: exception that triggered the retry
        on_retry: optional callback function

    returns:
        bool: true if should continue retrying, false if should stop
    """
    # if max_retries < 0, retry forever (never stop)
    if max_retries >= 0 and retry_count > max_retries:
        logger.error(
            "max retries ({max_retries}) reached. giving up. last error: {error}",
            max_retries=max_retries,
            error=str(e),
        )
        return False

    logger.warning(
        "attempt {count} failed. retrying in {delay:.2f} seconds. error: {error}",
        count=retry_count,
        delay=delay,
        error=str(e),
    )

    # call the custom on_retry function if provided
    if on_retry:
        on_retry(retry_count, delay, e)

    # sleep before retrying
    time.sleep(delay)
    return True


def retry_with_backoff(
    max_retries: int = DEFAULT_MAX_RETRIES,
    initial_delay: float = DEFAULT_INITIAL_DELAY,
    max_delay: float = DEFAULT_MAX_DELAY,
    backoff_factor: float = DEFAULT_BACKOFF_FACTOR,
    exceptions: Union[Type[Exception], List[Type[Exception]]] = DEFAULT_EXCEPTIONS,
    on_retry: Optional[Callable] = None,
):
    """
    decorator for retrying a function with exponential backoff on specified exceptions.

    args:
        max_retries: maximum number of retry attempts
        initial_delay: initial delay before first retry (seconds)
        max_delay: maximum delay between retries (seconds)
        backoff_factor: multiplier for delay between retries
        exceptions: exception or list of exceptions to catch and retry on
        on_retry: optional callback function to execute before each retry
            callback signature: on_retry(retry_count, delay, exception)

    returns:
        decorator function
    """
    if isinstance(exceptions, type) and issubclass(exceptions, Exception):
        exceptions = [exceptions]

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            retry_count = 0
            delay = initial_delay
            last_exception = None

            while True:  # we'll control the exit within the loop
                try:
                    return func(*args, **kwargs)
                except tuple(exceptions) as e:
                    last_exception = e
                    retry_count += 1

                    # calculate current delay (capped at max_delay)
                    current_delay = min(delay, max_delay)

                    # handle retry logic, logging, and callbacks
                    should_continue = _handle_retry(
                        retry_count, current_delay, max_retries, e, on_retry
                    )

                    if not should_continue:
                        break

                    # increase delay for next retry
                    delay = min(delay * backoff_factor, max_delay)

            # if we got here, all retries failed
            if last_exception:
                raise last_exception

            return None  # this should never be reached

        return wrapper

    return decorator
