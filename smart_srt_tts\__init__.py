"""
smart_srt_tts - dynamic-speed tts renderer for subtitles

highlights
----------
* json configuration-driven
* dynamic speech rate adjustments based on text density
* token-bucket rate limiter with automatic retries
* multi-threading for parallel processing
* structured logging and error handling
"""

from .srt_tts import SrtDynamicSpeechEngine, main

__version__ = "0.2.0"  # updated from 0.1.0 for json config
__all__ = ["SrtDynamicSpeechEngine", "main"]
