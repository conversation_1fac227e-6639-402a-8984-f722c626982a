"""
Configuration module for SRT-TTS system.

Provides:
- JSON configuration loading and validation
- Logging setup
- Strong typing via Pydantic models
"""

from .srt_tts_config import (
    Configuration,
    EngineSettings,
    LoggingConfig,
    LoggingHandlerConfig,
    load_config,
    setup_logging,
)

__all__ = [
    "Configuration",
    "EngineSettings",
    "LoggingConfig",
    "LoggingHandlerConfig",
    "load_config",
    "setup_logging",
]
