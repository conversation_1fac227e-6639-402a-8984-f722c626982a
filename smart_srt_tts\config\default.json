{"api_keys": ["sk_beebb851382b1189e1965380b3547631cf79781c1b7061d0", "sk_00ed1afd19923b6750ccb3becda0985f531ad5e2dc6e0498", "sk_f69bda702a992b6154ee930378c2ce1157016fe7a45d2c74", "sk_639f947baf9ca941f204029187325f711c7a4f614c5b7568", "sk_3267e646eb3149f8e70ff436314c503e9b03ad85c9ebf56c"], "output_filename": "dynamic_speech.mp3", "language": "en_US", "settings": {"voice_id": "UgBBYS2sOqTuMpoF3BR0", "model_id": "eleven_multilingual_v2", "metric": "wpm", "min_percentile": 5, "max_percentile": 95, "target_min_speed": 0.7, "target_max_speed": 1.2, "base_stability": 0.7, "base_similarity": 0.8, "use_speaker_boost": true, "max_workers": 2, "rate_limit": 2, "enable_smart_speed": true}, "output": {"base_directory": "data/output", "create_timestamped_subdirectory": true}, "logging": {"level": "DEBUG", "handlers": [{"type": "console", "colorize": true, "diagnose": true, "level": "DEBUG", "format": "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level:<8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | <level>{message}</level>"}]}}