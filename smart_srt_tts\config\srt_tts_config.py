"""
srt_tts_config.py - configuration loading and schema for srt-tts system.

provides:
- json configuration loading with environment variable fallback
- strong typing via Pydantic v2 models
- schema validation and sensible defaults

usage:
```python
from smart_srt_tts.config.srt_tts_config import load_config

config = load_config("path/to/config.json")
# or with environment variables
config = load_config()
```
"""

from __future__ import annotations

import json
import os
import time
from pathlib import Path
from typing import List, Optional, Union

# attempt to support both pydantic v1 and v2
try:  # pydantic ≥2
    from pydantic import BaseModel, Field, field_validator
    from pydantic_settings import BaseSettings

    _USE_PYDANTIC_V2 = True
except ImportError:  # pydantic 1.x fallback
    from pydantic import BaseSettings  # type: ignore
    from pydantic import BaseModel, Field
    from pydantic import validator as field_validator  # type: ignore

    _USE_PYDANTIC_V2 = False

from loguru import logger


def _env(key: str, default: str | None = None) -> str | None:
    """environment lookup shortcut."""
    return os.environ.get(key, default)


class LoggingHandlerConfig(BaseModel):
    """configuration for a single logger handler."""

    type: str
    level: str = "DEBUG"
    format: str = "{time} | {level} | {message}"
    colorize: bool = False
    diagnose: bool = False
    # file-specific options
    path: Optional[str] = None
    rotation: Optional[str] = None
    retention: Optional[str] = None
    # json formatter options
    serialize: bool = False
    backtrace: bool = False
    enqueue: bool = False


class LoggingConfig(BaseModel):
    """configuration for logging system."""

    level: str = "INFO"
    handlers: List[LoggingHandlerConfig] = Field(default_factory=list)


class OutputConfig(BaseModel):
    """output directory and file configuration."""

    base_directory: str = Field("data/output", description="base directory for output files")
    create_timestamped_subdirectory: bool = Field(
        True, description="create timestamped subdirectory for each run"
    )

    def get_full_output_path(self, filename: str) -> Path:
        """get full path for output file with appropriate directory structure."""
        base_dir = Path(self.base_directory)

        # create base directory if it doesn't exist
        if not base_dir.exists():
            base_dir.mkdir(parents=True, exist_ok=True)

        if self.create_timestamped_subdirectory:
            # create timestamped subdirectory
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            subdir = base_dir / timestamp
            subdir.mkdir(exist_ok=True)
            return subdir / filename
        else:
            # just use the base directory
            return base_dir / filename


class EngineSettings(BaseModel):
    """elevenlabs tts engine settings."""

    voice_id: str = Field("UgBBYS2sOqTuMpoF3BR0", description="elevenlabs voice id")
    model_id: str = Field("eleven_multilingual_v2", description="elevenlabs model id")
    metric: str = Field("wpm", description="'wpm' or 'sps'")
    min_percentile: int = 5
    max_percentile: int = 95
    target_min_speed: float = 0.70
    target_max_speed: float = 1.20
    base_stability: float = 0.7
    base_similarity: float = 0.8
    use_speaker_boost: bool = True
    max_workers: int = 4
    rate_limit: int = 4  # calls per minute
    enable_smart_speed: bool = Field(
        True, description="enable dynamic speech rate adjustment based on text density"
    )

    @field_validator("metric")
    @classmethod
    def _metric_valid(cls, v: str):
        v = v.lower()
        if v not in {"wpm", "sps"}:
            raise ValueError("metric must be 'wpm' or 'sps'")
        return v


class Configuration(BaseSettings):
    """main configuration model for srt-tts system."""

    # api_key is optional when api_keys list is supplied
    api_key: str = Field(default="", env="ELEVENLABS_API_KEY")
    api_keys: List[str] = Field(default_factory=list)
    current_api_key_index: int = 0
    output_filename: str = "output_dynamic_speech.mp3"
    language: str = "en_US"
    settings: EngineSettings = Field(default_factory=EngineSettings)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    output: OutputConfig = Field(default_factory=OutputConfig)

    if _USE_PYDANTIC_V2:

        class Config:
            env_nested_delimiter = "__"  # elevenlabs_settings__voice_id

    def get_output_path(self) -> Path:
        """get full output path with directory structure."""
        return self.output.get_full_output_path(self.output_filename)


def load_config(config_path: Optional[Union[str, Path]] = None) -> Configuration:
    """
    load configuration from json file or environment variables.

    order of precedence:
    1. explicit config_path json file
    2. default config file in standard location
    3. environment variables + defaults

    args:
        config_path: optional path to json config file

    returns:
        validated configuration object
    """
    # if explicit path provided, try to load it
    if config_path:
        config_path = Path(config_path)
        if config_path.exists():
            logger.info(f"loading configuration from {config_path}")
            try:
                config_dict = json.loads(config_path.read_text(encoding="utf-8"))
                return (
                    Configuration.model_validate(config_dict)
                    if _USE_PYDANTIC_V2
                    else Configuration(**config_dict)
                )
            except Exception as e:
                logger.error(f"failed to load configuration from {config_path}: {e}")
                logger.warning("falling back to default configuration")

    # check for default config in standard locations
    module_dir = Path(__file__).parent
    default_config = module_dir / "default.json"

    if default_config.exists():
        logger.info(f"loading default configuration from {default_config}")
        try:
            config_dict = json.loads(default_config.read_text(encoding="utf-8"))
            return (
                Configuration.model_validate(config_dict)
                if _USE_PYDANTIC_V2
                else Configuration(**config_dict)
            )
        except Exception as e:
            logger.warning(f"failed to load default configuration: {e}")

    # fallback to environment variables + defaults
    logger.info("using configuration from environment variables and defaults")
    try:
        return Configuration()
    except Exception as e:
        logger.error(f"failed to create configuration from environment: {e}")
        raise ValueError("no valid configuration source available") from e


def setup_logging(config: Configuration) -> None:
    """
    configure loguru logger based on config.

    args:
        config: application configuration object
    """
    # remove default logger
    logger.remove()

    # if no handlers configured, set up default console handler
    if not config.logging.handlers:
        logger.add(
            sink=os.sys.stderr,
            format=(
                "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
                "<level>{level:<8}</level> | "
                "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
                "<level>{message}</level>"
            ),
            colorize=True,
            level=config.logging.level,
        )
        return

    # configure handlers from config
    for handler in config.logging.handlers:
        handler_type = handler.type

        # simple approach - just handle the most common configurations
        if handler_type == "console":
            logger.add(
                sink=os.sys.stderr,
                level=handler.level,
                format=handler.format,
                colorize=handler.colorize,
                diagnose=handler.diagnose,
            )
        elif handler_type == "file":
            if not handler.path:
                file_path = "logs/app.log"
            else:
                file_path = handler.path

            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            # only add the parameters that are actually needed
            logger_params = {"sink": file_path, "level": handler.level, "format": handler.format}

            # add optional parameters only if they are set
            if handler.rotation is not None:
                logger_params["rotation"] = handler.rotation

            if handler.retention is not None:
                logger_params["retention"] = handler.retention

            if handler.serialize:
                logger_params["serialize"] = handler.serialize

            if handler.enqueue:
                logger_params["enqueue"] = handler.enqueue

            logger.add(**logger_params)
        else:
            logger.warning(f"unknown handler type: {handler_type}, skipping")

    logger.debug("logging configured successfully")
