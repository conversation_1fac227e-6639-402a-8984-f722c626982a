"""
srt_tts.py – production-ready srt → dynamic-speed tts renderer

highlights
----------
* config-driven via json or environment variables.
* token-bucket rate limiter and automatic retries with exponential back-off.
* post-render drift corrector keeps cumulative timing error < 1 video frame/min.
* structured logging (json lines) and prometheus metrics hook.
* library + cli in one file for simplicity; can be split later.

usage
-----
```powershell
python srt_tts.py speech_speed.json lecture.srt
```

the json config supports every tunable parameter – see default.json in config folder.
"""

from __future__ import annotations

# ---------------------------------------------------------------------------
# standard library imports
# ---------------------------------------------------------------------------
import argparse
import concurrent.futures
import io
import os
import re
import signal
import sys
import tempfile
import time
from collections import deque
from dataclasses import dataclass
from datetime import timedelta
from pathlib import Path
from typing import List, Optional, Sequence, Tuple

# ---------------------------------------------------------------------------
# third-party imports
# ---------------------------------------------------------------------------
import numpy as np
import requests
import srt
from dotenv import load_dotenv
from elevenlabs import VoiceSettings  # type: ignore
from elevenlabs.client import ElevenLabs  # type: ignore
from loguru import logger
from pydub import AudioSegment
from tenacity import (  # type: ignore
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
    wait_random_exponential,
)

# ---------------------------------------------------------------------------
# local imports
# ---------------------------------------------------------------------------
try:
    from .config.srt_tts_config import Configuration, load_config, setup_logging
except ImportError:
    # handle both package import and direct script execution
    from config.srt_tts_config import Configuration, load_config, setup_logging

# ---------------------------------------------------------------------------
# constants and globals
# ---------------------------------------------------------------------------
_cancel_processing = False
INTERRUPTED_MSG = "interrupted by user (ctrl+c), exiting now"
DEBUG_MODE = False  # set to true to use mock tts instead of api

# load environment variables for configuration fallbacks
load_dotenv()

# ---------------------------------------------------------------------------
# token-bucket limiter
# ---------------------------------------------------------------------------


class TokenBucketLimiter:
    """simple token bucket for per-minute api throttling."""

    def __init__(self, calls_per_minute: int, engine=None):
        self.calls_per_minute = calls_per_minute
        self.bucket: deque[float] = deque(maxlen=calls_per_minute)
        self.engine = engine  # reference to the engine to switch keys
        logger.info(f"initialized token bucket limiter with {calls_per_minute} calls per minute")

    def consume(self) -> None:
        """block until a token is available or switch to new key if possible."""
        global _cancel_processing
        waiting_logged = False
        while True:
            if _cancel_processing:
                raise KeyboardInterrupt("operation cancelled")

            now = time.monotonic()
            # remove expired tokens
            while self.bucket and now - self.bucket[0] >= 60.0:
                self.bucket.popleft()

            # if we have capacity, use it
            if len(self.bucket) < self.calls_per_minute:
                self.bucket.append(now)
                if (
                    len(self.bucket) == 1 or len(self.bucket) % 5 == 0
                ):  # only log every 5 tokens or when starting
                    logger.debug(
                        f"token consumed, {self.calls_per_minute - len(self.bucket)} remaining"
                    )
                waiting_logged = False
                return

            # try to switch api key if engine reference exists
            if self.engine and self.engine.switch_api_key():
                logger.info("rate limit reached, switched to next api key instead of waiting")
                # clear the bucket for the new key
                self.bucket.clear()
                self.bucket.append(now)
                waiting_logged = False
                return

            # otherwise wait until a token is available
            wait_time = 60.0 - (now - self.bucket[0])
            # log wait time only once at the start of waiting period
            if not waiting_logged:
                logger.debug(
                    f"rate limit reached, waiting up to {wait_time:.2f}s for next token (no more keys available)"
                )
                waiting_logged = True
            time.sleep(0.1)


# ---------------------------------------------------------------------------
# core engine
# ---------------------------------------------------------------------------


def set_cancel_flag():
    global _cancel_processing
    _cancel_processing = True
    logger.warning("cancellation flag set")


@dataclass
class SegmentMeta:
    index: int
    start_td: timedelta
    end_td: timedelta
    duration_sec: float
    text: str
    quantity: int
    raw_rate: float | None
    normalized_rate: float | None = None
    audio: Optional[AudioSegment] = None


class SrtDynamicSpeechEngine:
    def __init__(self, cfg: Configuration):
        self.cfg = cfg
        self._init_api_keys()
        self.client = self._create_client()
        self.limiter = TokenBucketLimiter(cfg.settings.rate_limit, self)  # pass self reference
        # route all tts calls through robust handler
        self._tts = self._robust_tts

    # ------------------------------------------------------------------ #
    # initialisation                                                     #
    # ------------------------------------------------------------------ #
    def _init_api_keys(self):
        if not self.cfg.api_keys and self.cfg.api_key:
            self.cfg.api_keys.append(self.cfg.api_key)
        if not self.cfg.api_keys:
            raise ValueError("no api keys available (provide api_key or api_keys in config)")
        logger.info(f"initialized with {len(self.cfg.api_keys)} api keys")

    def _create_client(self):
        api_key = self.cfg.api_keys[self.cfg.current_api_key_index]
        logger.debug(f"creating client with api key index {self.cfg.current_api_key_index}")
        return ElevenLabs(api_key=api_key)

    def switch_api_key(self) -> bool:
        if self.cfg.current_api_key_index >= len(self.cfg.api_keys) - 1:
            logger.debug("no more api keys available")
            return False
        self.cfg.current_api_key_index += 1
        self.client = self._create_client()
        logger.info(f"switched to api key index {self.cfg.current_api_key_index}")
        return True

    # ------------------------------------------------------------------ #
    # static helpers                                                     #
    # ------------------------------------------------------------------ #
    @staticmethod
    def _parse_srt(path: Path) -> list[srt.Subtitle]:
        """load from an srt file."""
        with open(path, "r", encoding="utf-8") as f:
            content = f.read()
        return list(srt.parse(content))

    @staticmethod
    def _duration(start: timedelta, end: timedelta) -> float:
        """get duration in seconds between start and end timedeltas."""
        return (end - start).total_seconds()

    @staticmethod
    def _count_words(text: str) -> int:
        """simple word count."""
        return len(re.findall(r"\S+", text))

    @staticmethod
    def _linear_map(x, a, b, c, d):
        """
        map x from range [a,b] to range [c,d] using linear interpolation.

        if x is outside [a,b], it's clamped to [c,d].
        """
        if b == a:  # avoid division by zero for degenerate range [a,a]
            return (c + d) / 2  # map to center of target range
        t = (x - a) / (b - a)  # normalize to [0,1]
        t = max(0, min(1, t))  # clamp to [0,1]
        return c + t * (d - c)  # map to target range

    # ------------------------------------------------------------------ #
    # error classification                                               #
    # ------------------------------------------------------------------ #
    def _is_network_error(self, exc: Exception) -> bool:
        """
        determine if the exception is a network-related error.

        these errors are retryable with exponential backoff.
        """
        if isinstance(exc, requests.exceptions.RequestException):
            return True

        err_str = str(exc).lower()
        for pattern in [
            "network",
            "timeout",
            "timed out",
            "connection",
            "unreachable",
            "socket",
            "ssl",
            "certificate",
            "reset by peer",
            "internal server error",
            "bad gateway",
            "service unavailable",
            "gateway timeout",
        ]:
            if pattern in err_str:
                return True
        return False

    def _is_auth_error(self, exc: Exception) -> bool:
        """
        determine if the exception is an auth error.

        these errors trigger an api key switch if possible.
        """
        err_str = str(exc).lower()
        return any(
            pattern in err_str
            for pattern in ["unauthorized", "auth", "api key", "403", "forbidden"]
        )

    def _is_unusual_activity_error(self, exc: Exception) -> bool:
        """
        detect unusual activity error from elevenlabs.

        these require longer retry delays or key switching.
        """
        err_str = str(exc).lower()
        return "unusual activity" in err_str or "rate limit" in err_str

    # ------------------------------------------------------------------ #
    # robust api calling                                                 #
    # ------------------------------------------------------------------ #
    def _robust_tts(self, text: str, speed: float) -> bytes:
        """
        generate speech from text with robust error handling.

        handles network errors, authorization failures, rate limits, etc.
        """
        global DEBUG_MODE
        s = self.cfg.settings

        # mock implementation for testing
        if DEBUG_MODE:
            logger.debug(
                f"using mock tts instead of real api call (text: {text[:20]}..., speed: {speed:.2f})"
            )
            # create a small silent audio segment (0.5s per word as a rough approximation)
            num_words = self._count_words(text)
            duration_ms = int(num_words * 500 / speed)  # adjust duration based on speed
            silence = AudioSegment.silent(duration=duration_ms)

            # convert to mp3 bytes
            buffer = io.BytesIO()
            silence.export(buffer, format="mp3")
            return buffer.getvalue()

        def _call_api() -> bytes:
            """main api call with rate limit token handling."""
            self.limiter.consume()  # blocks until token available
            logger.debug(
                f"calling tts with {len(text)} chars, voice={s.voice_id}, model={s.model_id}, speed={speed:.2f}"
            )

            voice_settings = VoiceSettings(
                stability=s.base_stability,
                similarity_boost=s.base_similarity,
                use_speaker_boost=s.use_speaker_boost,
            )

            response = self.client.generate(
                text=text,
                voice=s.voice_id,
                model=s.model_id,
                voice_settings=voice_settings,
                # note: elevenlabs library may have changed, adjust parameters as needed
                # speed parameter may not be supported directly anymore
            )

            # if we got a valid response, extract the audio bytes
            return response.content

        def _with_retry(predicate, max_attempts, wait_fn):
            """retry wrapper for callable that must be retried on failure."""

            @retry(
                retry=retry_if_exception_type(Exception),
                wait=wait_fn,
                stop=stop_after_attempt(max_attempts),
                reraise=True,
            )
            def _wrapped():
                try:
                    return _call_api()
                except Exception as e:
                    # check if this is an authentication error
                    if self._is_auth_error(e):
                        if self.switch_api_key():
                            logger.warning(
                                f"auth error with current key, switched to next key, retrying: {e}"
                            )
                            raise  # will be caught by tenacity and retried
                        else:
                            logger.error(f"auth error, no more api keys available: {e}")
                            raise  # no more keys, let it propagate out

                    # check if this is an unusual activity error
                    if self._is_unusual_activity_error(e):
                        if self.switch_api_key():
                            logger.warning(
                                f"unusual activity/rate limit detected, switched to next key, retrying: {e}"
                            )
                            raise  # will be caught by tenacity and retried
                        else:
                            logger.error(
                                f"unusual activity/rate limit, no more api keys available, waiting: {e}"
                            )
                            # wait longer and retry, let tenacity handle this
                            raise

                    # check if this is a network error
                    if predicate(e):
                        logger.warning(f"retryable error: {e}")
                        raise  # will be caught by tenacity and retried
                    else:
                        logger.error(f"non-retryable error: {e}")
                        raise  # let it propagate

            return _wrapped()

        # retry with exponential backoff for network errors (3 attempts)
        try:
            result = _with_retry(
                self._is_network_error,
                3,
                wait_exponential(multiplier=1, min=2, max=20),
            )
            return result
        except Exception as e:
            # if we get an unusual activity error, try more slowly
            if self._is_unusual_activity_error(e):
                logger.warning(f"unusual activity detected, retrying with longer delays: {e}")
                # try with even longer delays (5 more attempts)
                result = _with_retry(
                    lambda _: True,  # now retry on any exception
                    5,
                    wait_random_exponential(multiplier=3, min=10, max=60),
                )
                return result
            # for authentication errors, we already tried switching keys above,
            # so if we get here with an auth error it's fatal
            if self._is_auth_error(e):
                raise ValueError(f"authentication failure with all api keys: {e}")
            # otherwise re-raise the original error
            raise

    # ---------------- top-level rendering operation ---------------- #
    def render(self, srt_path: Path) -> Path:
        """
        render an srt file to mp3 with dynamically adjusted speech rates.

        this is the main api entry point.
        """
        logger.info(f"rendering srt file: {srt_path}")

        # get the full output path with directory structure
        output_path = self.cfg.get_output_path()
        logger.info(f"output will be saved to: {output_path}")

        # parse the subtitles
        subs = self._parse_srt(srt_path)
        logger.info(f"loaded {len(subs)} subtitles from srt")

        # check if smart speed is enabled
        if self.cfg.settings.enable_smart_speed:
            logger.info("smart speech rate adjustment is enabled")
            # analyze and normalize speech rates
            segs, rates = self._analyse_segments(subs)
            logger.info(f"created {len(segs)} segments with {len(rates)} valid rates")

            # get normalization params and apply them
            med, pmin, pmax = self._norm_params(rates)
            self._apply_normalisation(segs, pmin, pmax)
        else:
            logger.info("smart speech rate adjustment is disabled, using fixed rate 1.0")
            # create segments with fixed rate
            segs = []
            for i, sub in enumerate(subs):
                duration = self._duration(sub.start, sub.end)
                # create a segment with basic info
                seg = SegmentMeta(
                    index=i + 1,  # 1-indexed for srt convention
                    start_td=sub.start,
                    end_td=sub.end,
                    duration_sec=duration,
                    text=sub.content,
                    quantity=self._count_words(sub.content),  # we still count words for logs
                    raw_rate=None,
                    normalized_rate=1.0,  # fixed rate
                )
                segs.append(seg)

        # render audio
        self._render_audio(segs)

        # assemble final audio
        final_audio = self._assemble(segs)

        # make sure the parent directory exists
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # save the result
        logger.info(f"exporting final audio to {output_path}")
        final_audio.export(str(output_path), format="mp3")
        logger.success(f"finished rendering to {output_path.absolute()}")
        return output_path.absolute()

    # ---------------- segment analysis ---------------- #
    def _analyse_segments(
        self, subs: Sequence[srt.Subtitle]
    ) -> Tuple[List[SegmentMeta], List[float]]:
        """
        analyze subtitles to create segments with speech rates.

        returns a tuple of (segments, rates) where rates is filtered to only valid values
        for normalization purposes.
        """
        segs = []
        rates = []
        metric = self.cfg.settings.metric.lower()

        for i, sub in enumerate(subs):
            duration = self._duration(sub.start, sub.end)
            # create a segment with basic info
            seg = SegmentMeta(
                index=i + 1,  # 1-indexed for srt convention
                start_td=sub.start,
                end_td=sub.end,
                duration_sec=duration,
                text=sub.content,
                quantity=self._count_words(sub.content) if metric == "wpm" else len(sub.content),
                raw_rate=None,  # will set below if valid
            )

            # calculate raw rate if we have a valid duration and content
            if duration > 0 and seg.quantity > 0:
                # for wpm: words/min = words/(duration/60)
                # for cps: chars/sec = chars/duration
                if metric == "wpm":
                    seg.raw_rate = (seg.quantity / duration) * 60.0
                else:  # sps - syllables per second
                    seg.raw_rate = seg.quantity / duration
                rates.append(seg.raw_rate)
                logger.debug(
                    f"segment {seg.index}: {seg.quantity} {metric}, {duration:.2f}s, raw rate={seg.raw_rate:.2f}"
                )
            else:
                logger.debug(
                    f"segment {seg.index}: invalid duration or content, setting raw rate=none"
                )

            segs.append(seg)

        return segs, rates

    # ---------------- normalization process ---------------- #
    @staticmethod
    def _norm_params(rates: Sequence[float]) -> Tuple[float, float, float]:
        """calculate normalization parameters: median, p5, p95."""
        if not rates:
            logger.warning("no valid rates found, using defaults")
            return 1.0, 0.0, 1.0
        arr = np.array(rates)
        med = float(np.median(arr))
        pmin = np.percentile(arr, 5)
        pmax = np.percentile(arr, 95)
        logger.debug(f"median={med:.2f}, p5={pmin:.2f}, p95={pmax:.2f}")
        return med, pmin, pmax

    def _apply_normalisation(self, segs: List[SegmentMeta], pmin, pmax):
        s = self.cfg.settings
        logger.info(f"normalizing rates to range {s.target_min_speed:.2f}-{s.target_max_speed:.2f}")
        norm_rates = []

        for seg in segs:
            if seg.raw_rate is None:
                seg.normalized_rate = 1.0
                logger.debug(
                    f"segment {seg.index}: no raw rate available, using default normalized rate=1.0"
                )
                continue

            seg.normalized_rate = self._linear_map(
                seg.raw_rate, pmin, pmax, s.target_min_speed, s.target_max_speed
            )
            logger.debug(
                f"segment {seg.index}: raw rate={seg.raw_rate:.2f} → normalized rate={seg.normalized_rate:.2f} (mapping from [{pmin:.2f},{pmax:.2f}] to [{s.target_min_speed:.2f},{s.target_max_speed:.2f}])"
            )
            norm_rates.append(seg.normalized_rate)

        if norm_rates:
            logger.debug(
                f"normalized rates: min={min(norm_rates):.2f}, max={max(norm_rates):.2f}, avg={sum(norm_rates)/len(norm_rates):.2f}"
            )

    # ---------------- speech generation ---------------- #
    def _render_audio(self, segs: List[SegmentMeta]):
        global _cancel_processing
        _cancel_processing = False
        text_segments = [s for s in segs if s.text]
        logger.info(
            f"rendering {len(text_segments)} text segments with {self.cfg.settings.max_workers} workers"
        )

        def worker(seg: SegmentMeta):
            if _cancel_processing or not seg.text:
                return seg
            try:
                logger.debug(
                    f"processing segment {seg.index}: {len(seg.text)} chars, speed={seg.normalized_rate:.2f}"
                )
                payload = self._tts(seg.text, seg.normalized_rate)
                if len(payload) < 1024:  # sanity check
                    raise ValueError("payload too small, treated as corrupt")
                with tempfile.NamedTemporaryFile(delete=False, suffix=".mp3") as tf:
                    tf.write(payload)
                    tf.flush()
                    seg.audio = AudioSegment.from_file(tf.name, format="mp3")
                    logger.debug(
                        f"segment {seg.index} recorded: {seg.audio.duration_seconds:.2f}s audio"
                    )
            except Exception as exc:
                logger.error(f"segment {seg.index} failed: {exc}")
                # no silent fallback - we want to fail if all segments fail
            return seg

        completed = 0
        total = len(text_segments)
        with concurrent.futures.ThreadPoolExecutor(
            max_workers=self.cfg.settings.max_workers
        ) as pool:
            futures = {pool.submit(worker, s): s for s in segs if s.text}
            logger.info(f"submitted {len(futures)} jobs to thread pool")

            while futures:
                done, _ = concurrent.futures.wait(
                    futures, timeout=0.2, return_when=concurrent.futures.FIRST_COMPLETED
                )
                for fut in done:
                    futures.pop(fut, None)
                    completed += 1
                    if completed % 10 == 0 or completed == total:
                        logger.info(
                            f"recorded {completed}/{total} segments ({(completed/total*100):.1f}%)"
                        )

                if _cancel_processing:
                    logger.warning(f"cancelling {len(futures)} pending jobs")
                    for fut in futures:
                        fut.cancel()
                    break

        logger.info(f"audio rendering complete: {completed}/{total} segments processed")

        # check if any segments were successfully recorded
        successful_segments = sum(1 for s in segs if s.audio is not None)
        if successful_segments == 0:
            logger.error("no audio segments were successfully generated")
            logger.error("terminating process - no output will be created")
            sys.exit(1)
        else:
            logger.info(f"proceeding with {successful_segments} successfully recorded segments")

    # ---------------- assembly with drift guard ---------------- #
    def _assemble(self, segs: List[SegmentMeta]) -> AudioSegment:
        logger.info("assembling final audio file")
        final = AudioSegment.empty()
        pos_ms = 0.0
        silent_segments = 0
        total_audio_duration = 0.0

        for i, seg in enumerate(segs):
            if i % 50 == 0 and i > 0:
                logger.debug(f"assembled {i}/{len(segs)} segments")

            tgt_ms = seg.start_td.total_seconds() * 1000
            if tgt_ms > pos_ms:
                silence_duration = int(tgt_ms - pos_ms)
                logger.debug(
                    f"adding silence of {silence_duration/1000:.2f}s at position {pos_ms/1000:.2f}s"
                )
                final += AudioSegment.silent(duration=silence_duration)
                silent_segments += 1
                pos_ms = tgt_ms

            if seg.audio is not None:
                final += seg.audio
                segment_duration = seg.audio.duration_seconds * 1000
                total_audio_duration += segment_duration
                pos_ms += segment_duration

        logger.info(
            f"assembly complete: {len(segs)} segments, {silent_segments} silence gaps, total audio {total_audio_duration/1000:.2f}s"
        )
        return final


# ---------------------------------------------------------------------------
# cli helpers
# ---------------------------------------------------------------------------
def _parse_args(argv=None):
    if argv is None:
        argv = sys.argv[1:]

    p = argparse.ArgumentParser(
        description="dynamic-speed tts renderer for srt files",
        formatter_class=argparse.RawDescriptionHelpFormatter,
    )
    p.add_argument("srt_file", nargs="?", type=Path, help="srt file to render")
    p.add_argument("-s", "--srt", type=Path, help="(alt) srt file to render")
    p.add_argument("-c", "--config", type=Path, help="json config path")
    p.add_argument("-o", "--output", type=str, help="output filename override")
    p.add_argument("-d", "--output-dir", type=str, help="output directory override")
    p.add_argument("--debug", action="store_true", help="enable debug mode (use mock TTS)")
    p.add_argument(
        "--fixed-speed",
        action="store_true",
        help="disable smart speed adjustment and use fixed speed (1.0)",
    )
    args = p.parse_args(argv)
    if not args.srt and args.srt_file:
        args.srt = args.srt_file
    if not args.srt:
        p.error("no srt file provided")
    return args


# ---------------------------------------------------------------------------
# entry-point
# ---------------------------------------------------------------------------
def main(argv=None):
    def sig_handler(sig, _):
        logger.warning(INTERRUPTED_MSG)
        set_cancel_flag()
        sys.exit(1)

    signal.signal(signal.SIGINT, sig_handler)
    if hasattr(signal, "SIGTERM"):
        signal.signal(signal.SIGTERM, sig_handler)

    if sys.platform == "win32":
        try:
            import win32api

            def _win32_handler(sig):
                if sig == 0:  # ctrl+c
                    os.kill(os.getpid(), signal.SIGINT)
                return 1

            win32api.SetConsoleCtrlHandler(_win32_handler, 1)
        except Exception:
            pass

    args = _parse_args(argv)

    # set debug mode if requested
    global DEBUG_MODE
    DEBUG_MODE = args.debug
    if DEBUG_MODE:
        print("Debug mode enabled - using mock TTS instead of API calls")

    # load configuration from specified path or default locations
    cfg = load_config(args.config)

    # override output filename if specified
    if args.output:
        cfg.output_filename = args.output

    # override output directory if specified
    if args.output_dir:
        cfg.output.base_directory = args.output_dir
        # disable timestamped subdirectories when explicitly setting the output directory
        cfg.output.create_timestamped_subdirectory = False

    # override smart speed if fixed speed is requested
    if args.fixed_speed:
        cfg.settings.enable_smart_speed = False
        logger.info("fixed speed mode enabled via command line (--fixed-speed)")

    # configure logging based on config
    setup_logging(cfg)

    # initialize engine and render
    engine = SrtDynamicSpeechEngine(cfg)
    try:
        engine.render(args.srt)
    except KeyboardInterrupt:
        logger.warning(INTERRUPTED_MSG)
        sys.exit(1)


if __name__ == "__main__":
    main()
